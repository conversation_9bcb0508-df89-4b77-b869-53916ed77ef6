<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="crypto.learning.CryptoIntegrationTest" time="0.113" tests="3" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703143057443_3.jar /Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire 2025-07-03T14-30-57_366-jvmRun1 surefire-20250703143057443_1tmp surefire_0-20250703143057443_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="CryptoIntegrationTest"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703143057443_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testManInTheMiddleDetection" classname="crypto.learning.CryptoIntegrationTest" time="0.031">
    <system-out><![CDATA[🕵️ 开始中间人攻击检测测试
✅ 成功检测到中间人攻击
✅ 数字签名有效防止了身份伪造

]]></system-out>
  </testcase>
  <testcase name="testForwardSecrecy" classname="crypto.learning.CryptoIntegrationTest" time="0.061">
    <system-out><![CDATA[🔄 开始前向安全性测试
✅ 会话 1 加密完成
✅ 会话 2 加密完成
✅ 会话 3 加密完成
🚨 模拟会话2的密钥泄露
⚠️ 会话2被解密: Session 2 message
✅ 会话1保持安全
✅ 会话3保持安全
✅ 前向安全性验证成功

]]></system-out>
  </testcase>
  <testcase name="testCompleteEncryptionFlow" classname="crypto.learning.CryptoIntegrationTest" time="0.006">
    <system-out><![CDATA[🚀 开始完整加密通信流程测试
==================================================
👤 第一步：生成Alice和Bob的身份
✅ Alice身份生成完成
✅ Bob身份生成完成

🤝 第二步：密钥协商
✅ 共享密钥长度: 32 字节
✅ 通信密钥长度: 32 字节
✅ MAC密钥长度: 32 字节

📝 第三步：Alice发送加密消息给Bob
原始消息: Hello Bob! This is a secure message from Alice.
✅ 消息加密完成，密文长度: 63 字节
✅ 消息签名完成，签名长度: 64 字节

📨 第四步：Bob接收和验证消息
✅ Alice的签名验证成功
✅ 消息解密成功: Hello Bob! This is a secure message from Alice.

🔍 第五步：完整性验证
✅ 会话完整性哈希: 3623431305f5d6e9d10f0e144f4227ed...

🎉 完整加密通信流程测试成功！
==================================================
]]></system-out>
  </testcase>
</testsuite>