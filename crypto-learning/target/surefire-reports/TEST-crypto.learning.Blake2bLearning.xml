<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="crypto.learning.Blake2bLearning" time="0.081" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703142959383_3.jar /Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire 2025-07-03T14-29-59_264-jvmRun1 surefire-20250703142959383_1tmp surefire_0-20250703142959383_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703142959383_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testPerformance" classname="crypto.learning.Blake2bLearning" time="0.059">
    <system-out><![CDATA[⚡ 开始Blake2b性能测试
📊 性能统计 (10000 次操作, 1KB数据):
  哈希平均时间: 0.003 ms
  MAC平均时间: 0.002 ms
✅ Blake2b性能测试完成

]]></system-out>
  </testcase>
  <testcase name="testBasicHashing" classname="crypto.learning.Blake2bLearning" time="0.001">
    <system-out><![CDATA[🔨 开始Blake2b基本哈希测试
📝 输入: Briar uses Blake2b for hashing and MAC
🔨 哈希长度: 32 字节
✅ 哈希一致性验证通过
✅ 不同输入产生不同哈希
✅ Blake2b基本哈希测试通过

]]></system-out>
  </testcase>
  <testcase name="testVariableLengthOutput" classname="crypto.learning.Blake2bLearning" time="0">
    <system-out><![CDATA[📏 开始Blake2b可变长度输出测试
✅ 16字节哈希: 9ca636b52205bbac...
✅ 32字节哈希: 648fbcfdc35aab96...
✅ 48字节哈希: 2fc8bf781b2455dc...
✅ 64字节哈希: 0de66fdc5ffa72c1...
✅ Blake2b可变长度输出测试通过

]]></system-out>
  </testcase>
  <testcase name="testBlake2bMAC" classname="crypto.learning.Blake2bLearning" time="0.001">
    <system-out><![CDATA[🔐 开始Blake2b MAC测试
📝 消息: Authenticated message with Blake2b
🔑 密钥长度: 32 字节
🔐 MAC长度: 32 字节
✅ MAC一致性验证通过
✅ 不同密钥产生不同MAC
✅ 不同消息产生不同MAC
✅ Blake2b MAC测试通过

]]></system-out>
  </testcase>
  <testcase name="testBlake2bParameters" classname="crypto.learning.Blake2bLearning" time="0.001">
    <system-out><![CDATA[⚙️ 开始Blake2b参数化测试
✅ 无参数哈希: 46faa1f4122006eb...
✅ 密钥哈希: 425fcb5126a3d45a...
✅ 密钥+盐哈希: 6a71db9f4d995255...
✅ 全参数哈希: b327a416ab00aeaa...
✅ Blake2b参数化测试通过

]]></system-out>
  </testcase>
  <testcase name="testComparisonWithOtherHashes" classname="crypto.learning.Blake2bLearning" time="0.003">
    <system-out><![CDATA[⚖️ 开始Blake2b与其他哈希函数比较
📊 哈希长度比较:
  Blake2b: 32 字节
  SHA-256: 32 字节
✅ 不同算法产生不同哈希值
✅ 哈希函数比较测试完成

]]></system-out>
  </testcase>
</testsuite>