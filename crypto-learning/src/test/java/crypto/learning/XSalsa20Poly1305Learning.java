package crypto.learning;

import org.bouncycastle.crypto.engines.XSalsa20Engine;
import org.bouncycastle.crypto.macs.Poly1305;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * XSalsa20Poly1305认证加密学习实践
 * 
 * 学习目标：
 * 1. 理解认证加密的工作原理
 * 2. 掌握XSalsa20流加密算法
 * 3. 学习Poly1305消息认证码
 * 4. 实现安全的加密解密操作
 */
public class XSalsa20Poly1305Learning {
    
    private static final int KEY_LENGTH = 32;  // 256位密钥
    private static final int IV_LENGTH = 24;   // 192位IV (XSalsa20扩展)
    private static final int MAC_LENGTH = 16;  // 128位MAC
    
    /**
     * 任务1.1：基本加密解密测试
     */
    @Test
    public void testBasicEncryptionDecryption() {
        System.out.println("🔐 开始XSalsa20Poly1305基本加密解密测试");
        
        // 1. 准备测试数据
        byte[] key = generateRandomBytes(KEY_LENGTH);
        byte[] iv = generateRandomBytes(IV_LENGTH);
        byte[] plaintext = "Hello, Briar Crypto Learning!".getBytes();
        
        System.out.println("📝 明文: " + new String(plaintext));
        System.out.println("🔑 密钥长度: " + key.length + " 字节");
        System.out.println("🎲 IV长度: " + iv.length + " 字节");
        
        // 2. 执行加密
        byte[] ciphertext = encrypt(plaintext, key, iv);
        
        // 3. 验证加密结果
        assertNotNull("密文不应为空", ciphertext);
        assertEquals("密文长度应为明文长度+MAC长度", 
                    plaintext.length + MAC_LENGTH, ciphertext.length);
        
        System.out.println("🔒 密文长度: " + ciphertext.length + " 字节");
        System.out.println("✅ 加密成功");
        
        // 4. 执行解密
        byte[] decrypted = decrypt(ciphertext, key, iv);
        
        // 5. 验证解密结果
        assertArrayEquals("解密后应恢复原始明文", plaintext, decrypted);
        
        System.out.println("🔓 解密结果: " + new String(decrypted));
        System.out.println("✅ XSalsa20Poly1305基本测试通过\n");
    }
    
    /**
     * 任务1.2：MAC验证失败测试
     */
    @Test
    public void testMACVerificationFailure() {
        System.out.println("🛡️ 开始MAC验证失败测试");
        
        byte[] key = generateRandomBytes(KEY_LENGTH);
        byte[] iv = generateRandomBytes(IV_LENGTH);
        byte[] plaintext = "Test message for MAC verification".getBytes();
        
        // 加密
        byte[] ciphertext = encrypt(plaintext, key, iv);
        
        // 篡改密文中的MAC部分
        ciphertext[0] ^= 1; // 翻转第一个字节的一位
        
        try {
            decrypt(ciphertext, key, iv);
            fail("篡改后的密文解密应该失败");
        } catch (SecurityException e) {
            System.out.println("✅ MAC验证正确检测到篡改: " + e.getMessage());
        }
        
        System.out.println("✅ MAC验证失败测试通过\n");
    }
    
    /**
     * 任务1.3：不同密钥测试
     */
    @Test
    public void testDifferentKeys() {
        System.out.println("🔑 开始不同密钥测试");
        
        byte[] key1 = generateRandomBytes(KEY_LENGTH);
        byte[] key2 = generateRandomBytes(KEY_LENGTH);
        byte[] iv = generateRandomBytes(IV_LENGTH);
        byte[] plaintext = "Same plaintext, different keys".getBytes();
        
        // 使用不同密钥加密相同明文
        byte[] ciphertext1 = encrypt(plaintext, key1, iv);
        byte[] ciphertext2 = encrypt(plaintext, key2, iv);
        
        // 验证密文不同
        assertFalse("不同密钥应产生不同密文", 
                   java.util.Arrays.equals(ciphertext1, ciphertext2));
        
        // 验证无法用错误密钥解密
        try {
            decrypt(ciphertext1, key2, iv);
            fail("错误密钥解密应该失败");
        } catch (SecurityException e) {
            System.out.println("✅ 错误密钥正确被拒绝: " + e.getMessage());
        }
        
        System.out.println("✅ 不同密钥测试通过\n");
    }
    
    /**
     * 实现XSalsa20Poly1305加密
     *
     * 加密流程：
     * 1. 初始化XSalsa20引擎
     * 2. 生成Poly1305子密钥
     * 3. 加密明文
     * 4. 计算MAC
     * 5. 组合MAC和密文
     */
    public byte[] encrypt(byte[] plaintext, byte[] key, byte[] iv) {
        try {
            // 1. 初始化XSalsa20引擎
            XSalsa20Engine xsalsa20 = new XSalsa20Engine();
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            xsalsa20.init(true, params);
            
            // 2. 生成Poly1305子密钥
            // 使用32字节的零作为输入，生成Poly1305密钥
            byte[] subKey = new byte[32];
            byte[] zero = new byte[32];
            xsalsa20.processBytes(zero, 0, 32, subKey, 0);
            
            // 3. 加密明文
            byte[] encrypted = new byte[plaintext.length];
            xsalsa20.processBytes(plaintext, 0, plaintext.length, encrypted, 0);
            
            // 4. 计算MAC
            Poly1305 poly1305 = new Poly1305();
            poly1305.init(new KeyParameter(subKey));
            poly1305.update(encrypted, 0, encrypted.length);
            byte[] mac = new byte[MAC_LENGTH];
            poly1305.doFinal(mac, 0);
            
            // 5. 组合MAC和密文 (MAC在前，密文在后)
            byte[] result = new byte[MAC_LENGTH + encrypted.length];
            System.arraycopy(mac, 0, result, 0, MAC_LENGTH);
            System.arraycopy(encrypted, 0, result, MAC_LENGTH, encrypted.length);
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }
    
    /**
     * 实现XSalsa20Poly1305解密
     *
     * 解密流程：
     * 1. 分离MAC和密文
     * 2. 初始化XSalsa20引擎
     * 3. 生成Poly1305子密钥
     * 4. 验证MAC
     * 5. 解密密文
     */
    public byte[] decrypt(byte[] ciphertext, byte[] key, byte[] iv) {
        try {
            // 1. 分离MAC和密文
            if (ciphertext.length < MAC_LENGTH) {
                throw new IllegalArgumentException("密文长度不足");
            }
            
            byte[] mac = new byte[MAC_LENGTH];
            byte[] encrypted = new byte[ciphertext.length - MAC_LENGTH];
            System.arraycopy(ciphertext, 0, mac, 0, MAC_LENGTH);
            System.arraycopy(ciphertext, MAC_LENGTH, encrypted, 0, encrypted.length);
            
            // 2. 初始化XSalsa20引擎
            XSalsa20Engine xsalsa20 = new XSalsa20Engine();
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            xsalsa20.init(false, params);
            
            // 3. 生成Poly1305子密钥
            byte[] subKey = new byte[32];
            byte[] zero = new byte[32];
            xsalsa20.processBytes(zero, 0, 32, subKey, 0);
            
            // 4. 验证MAC
            Poly1305 poly1305 = new Poly1305();
            poly1305.init(new KeyParameter(subKey));
            poly1305.update(encrypted, 0, encrypted.length);
            byte[] expectedMac = new byte[MAC_LENGTH];
            poly1305.doFinal(expectedMac, 0);
            
            if (!constantTimeEquals(mac, expectedMac)) {
                throw new SecurityException("MAC验证失败 - 数据可能被篡改");
            }
            
            // 5. 解密
            byte[] plaintext = new byte[encrypted.length];
            xsalsa20.processBytes(encrypted, 0, encrypted.length, plaintext, 0);
            
            return plaintext;
        } catch (Exception e) {
            if (e instanceof SecurityException) {
                throw e;
            }
            throw new RuntimeException("解密失败", e);
        }
    }
    
    /**
     * 常量时间比较函数
     * 防止时序攻击
     * 
     * 重要：即使字节数组长度不同或内容不同，
     * 这个函数的执行时间也应该是恒定的
     */
    private boolean constantTimeEquals(byte[] a, byte[] b) {
        if (a.length != b.length) return false;
        
        int result = 0;
        for (int i = 0; i < a.length; i++) {
            result |= a[i] ^ b[i];
        }
        return result == 0;
    }
    
    /**
     * 工具函数：生成安全随机字节
     */
    private byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        new java.security.SecureRandom().nextBytes(bytes);
        return bytes;
    }
}
