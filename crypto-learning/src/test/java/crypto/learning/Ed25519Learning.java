package crypto.learning;

import net.i2p.crypto.eddsa.*;
import net.i2p.crypto.eddsa.spec.*;
import org.junit.Test;
import java.security.*;
import java.security.spec.NamedParameterSpec;
import java.util.Arrays;
import java.security.KeyPairGenerator;
import static org.junit.Assert.*;

/**
 * Ed25519数字签名学习实践
 * 
 * 学习目标：
 * 1. 理解椭圆曲线数字签名算法
 * 2. 掌握Ed25519的密钥生成
 * 3. 学习数字签名的创建和验证
 * 4. 理解数字签名的安全属性
 */
public class Ed25519Learning {
    
    private static final EdDSANamedCurveSpec CURVE_SPEC = 
            EdDSANamedCurveTable.getByName("Ed25519");
    
    /**
     * 任务2.1：Ed25519密钥生成测试
     */
    @Test
    public void testKeyGeneration() {
        System.out.println("🔑 开始Ed25519密钥生成测试");
        
        // 1. 生成密钥对
        KeyPair keyPair = generateKeyPair();
        
        // 2. 验证密钥
        assertNotNull("私钥不应为空", keyPair.getPrivate());
        assertNotNull("公钥不应为空", keyPair.getPublic());
        
        // 3. 检查密钥类型和算法
        assertEquals("私钥算法应为EdDSA", "EdDSA", keyPair.getPrivate().getAlgorithm());
        assertEquals("公钥算法应为EdDSA", "EdDSA", keyPair.getPublic().getAlgorithm());
        
        // 4. 检查密钥编码长度（现代Java EdDSA实现）
        byte[] privateKeyEncoded = keyPair.getPrivate().getEncoded();
        byte[] publicKeyEncoded = keyPair.getPublic().getEncoded();
        
        assertNotNull("私钥编码不应为空", privateKeyEncoded);
        assertNotNull("公钥编码不应为空", publicKeyEncoded);
        
        System.out.println("✅ 私钥编码长度: " + privateKeyEncoded.length + " 字节");
        System.out.println("✅ 公钥编码长度: " + publicKeyEncoded.length + " 字节");
        System.out.println("✅ Ed25519密钥生成测试通过\n");
    }
    
    /**
     * 任务2.2：基本签名和验证测试
     */
    @Test
    public void testBasicSignatureVerification() throws Exception {
        System.out.println("✍️ 开始Ed25519基本签名验证测试");
        
        // 1. 生成密钥对
        KeyPair keyPair = generateKeyPair();
        
        // 2. 准备消息
        byte[] message = "Briar secure messaging with Ed25519".getBytes();
        System.out.println("📝 消息: " + new String(message));
        
        // 3. 生成签名
        byte[] signature = sign(message, keyPair.getPrivate());
        assertNotNull("签名不应为空", signature);
        assertEquals("Ed25519签名应为64字节", 64, signature.length);
        
        System.out.println("✍️ 签名长度: " + signature.length + " 字节");
        
        // 4. 验证签名
        boolean isValid = verify(message, signature, keyPair.getPublic());
        assertTrue("签名验证应该成功", isValid);
        
        System.out.println("✅ 签名验证成功");
        System.out.println("✅ Ed25519基本签名验证测试通过\n");
    }
    
    /**
     * 任务2.3：错误消息签名验证测试
     */
    @Test
    public void testInvalidMessageVerification() throws Exception {
        System.out.println("❌ 开始错误消息签名验证测试");
        
        KeyPair keyPair = generateKeyPair();
        
        // 原始消息和签名
        byte[] originalMessage = "Original message".getBytes();
        byte[] signature = sign(originalMessage, keyPair.getPrivate());
        
        // 测试不同的错误消息
        byte[] wrongMessage = "Wrong message".getBytes();
        boolean isInvalid = verify(wrongMessage, signature, keyPair.getPublic());
        assertFalse("错误消息的签名验证应该失败", isInvalid);
        
        System.out.println("✅ 错误消息正确被拒绝");
        
        // 测试篡改的签名
        byte[] tamperedSignature = signature.clone();
        tamperedSignature[0] ^= 1; // 修改签名的第一个字节
        
        boolean isTamperedInvalid = verify(originalMessage, tamperedSignature, keyPair.getPublic());
        assertFalse("篡改的签名验证应该失败", isTamperedInvalid);
        
        System.out.println("✅ 篡改的签名正确被拒绝");
        System.out.println("✅ 错误消息签名验证测试通过\n");
    }
    
    /**
     * 任务2.4：不同密钥对签名测试
     */
    @Test
    public void testDifferentKeyPairs() throws Exception {
        System.out.println("🔐 开始不同密钥对签名测试");
        
        // 生成两个不同的密钥对
        KeyPair keyPair1 = generateKeyPair();
        KeyPair keyPair2 = generateKeyPair();
        
        byte[] message = "Test message for different keys".getBytes();
        
        // 用第一个密钥签名
        byte[] signature1 = sign(message, keyPair1.getPrivate());
        
        // 用第二个密钥验证（应该失败）
        boolean crossVerify = verify(message, signature1, keyPair2.getPublic());
        assertFalse("不同密钥对的签名验证应该失败", crossVerify);
        
        System.out.println("✅ 不同密钥对正确被拒绝");
        
        // 用正确的密钥验证（应该成功）
        boolean correctVerify = verify(message, signature1, keyPair1.getPublic());
        assertTrue("正确密钥对的签名验证应该成功", correctVerify);
        
        System.out.println("✅ 正确密钥对验证成功");
        System.out.println("✅ 不同密钥对签名测试通过\n");
    }
    
    /**
     * 任务2.5：确定性签名测试
     */
    @Test
    public void testDeterministicSignature() throws Exception {
        System.out.println("🎯 开始确定性签名测试");
        
        KeyPair keyPair = generateKeyPair();
        byte[] message = "Deterministic signature test".getBytes();
        
        // 多次签名相同消息
        byte[] signature1 = sign(message, keyPair.getPrivate());
        byte[] signature2 = sign(message, keyPair.getPrivate());
        byte[] signature3 = sign(message, keyPair.getPrivate());
        
        // Ed25519是确定性的，相同消息应产生相同签名
        assertArrayEquals("相同消息应产生相同签名", signature1, signature2);
        assertArrayEquals("相同消息应产生相同签名", signature2, signature3);
        
        System.out.println("✅ Ed25519确定性签名验证通过");
        System.out.println("✅ 确定性签名测试通过\n");
    }
    
    /**
     * 任务2.6：性能测试
     */
    @Test
    public void testPerformance() throws Exception {
        System.out.println("⚡ 开始Ed25519性能测试");
        
        KeyPair keyPair = generateKeyPair();
        byte[] message = "Performance test message".getBytes();
        
        int iterations = 1000;
        
        // 测试签名性能
        long signStart = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            sign(message, keyPair.getPrivate());
        }
        long signEnd = System.currentTimeMillis();
        
        // 测试验证性能
        byte[] signature = sign(message, keyPair.getPrivate());
        long verifyStart = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            verify(message, signature, keyPair.getPublic());
        }
        long verifyEnd = System.currentTimeMillis();
        
        double signTime = (signEnd - signStart) / (double) iterations;
        double verifyTime = (verifyEnd - verifyStart) / (double) iterations;
        
        System.out.println("📊 性能统计 (" + iterations + " 次操作):");
        System.out.println("  签名平均时间: " + String.format("%.2f", signTime) + " ms");
        System.out.println("  验证平均时间: " + String.format("%.2f", verifyTime) + " ms");
        System.out.println("✅ Ed25519性能测试完成\n");
    }
    
    // ========== 实现方法 ==========
    
    /**
     * 生成Ed25519密钥对
     */
    private KeyPair generateKeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("EdDSA");
            // 使用现代Java的NamedParameterSpec而不是过时的EdDSANamedCurveSpec
            keyGen.initialize(new NamedParameterSpec("Ed25519"));
            return keyGen.generateKeyPair();
        } catch (Exception e) {
            throw new RuntimeException("密钥生成失败", e);
        }
    }
    
    /**
     * 使用Ed25519私钥签名消息
     */
    private byte[] sign(byte[] message, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("EdDSA");
        signature.initSign(privateKey);
        signature.update(message);
        return signature.sign();
    }
    
    /**
     * 使用Ed25519公钥验证签名
     */
    private boolean verify(byte[] message, byte[] signature, PublicKey publicKey) {
        try {
            Signature verifier = Signature.getInstance("EdDSA");
            verifier.initVerify(publicKey);
            verifier.update(message);
            return verifier.verify(signature);
        } catch (Exception e) {
            return false;
        }
    }
}
