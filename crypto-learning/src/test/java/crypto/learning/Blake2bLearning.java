package crypto.learning;

import org.bouncycastle.crypto.digests.Blake2bDigest;
import org.junit.Test;
import java.util.Arrays;
import static org.junit.Assert.*;

/**
 * Blake2b哈希函数学习实践
 * 
 * 学习目标：
 * 1. 理解Blake2b哈希函数的特点
 * 2. 掌握Blake2b的基本使用
 * 3. 学习Blake2b作为MAC的应用
 * 4. 理解哈希函数的安全属性
 */
public class Blake2bLearning {
    
    /**
     * 任务3.1：Blake2b基本哈希测试
     */
    @Test
    public void testBasicHashing() {
        System.out.println("🔨 开始Blake2b基本哈希测试");
        
        // 1. 准备测试数据
        byte[] input = "<PERSON><PERSON><PERSON> uses Blake2b for hashing and MAC".getBytes();
        System.out.println("📝 输入: " + new String(input));
        
        // 2. 计算哈希
        byte[] hash1 = hash(input);
        byte[] hash2 = hash(input);
        
        // 3. 验证一致性
        assertArrayEquals("相同输入应产生相同哈希", hash1, hash2);
        assertEquals("哈希长度应为32字节", 32, hash1.length);
        
        System.out.println("🔨 哈希长度: " + hash1.length + " 字节");
        System.out.println("✅ 哈希一致性验证通过");
        
        // 4. 测试不同输入
        byte[] differentInput = "Different input for Blake2b".getBytes();
        byte[] differentHash = hash(differentInput);
        assertFalse("不同输入应产生不同哈希", 
                   Arrays.equals(hash1, differentHash));
        
        System.out.println("✅ 不同输入产生不同哈希");
        System.out.println("✅ Blake2b基本哈希测试通过\n");
    }
    
    /**
     * 任务3.2：Blake2b可变长度输出测试
     */
    @Test
    public void testVariableLengthOutput() {
        System.out.println("📏 开始Blake2b可变长度输出测试");
        
        byte[] input = "Variable length output test".getBytes();
        
        // 测试不同的输出长度
        int[] outputLengths = {16, 32, 48, 64};
        
        for (int length : outputLengths) {
            byte[] hash = hashWithLength(input, length);
            assertEquals("哈希长度应为" + length + "字节", length, hash.length);
            System.out.println("✅ " + length + "字节哈希: " + bytesToHex(hash, 8));
        }
        
        System.out.println("✅ Blake2b可变长度输出测试通过\n");
    }
    
    /**
     * 任务3.3：Blake2b MAC测试
     */
    @Test
    public void testBlake2bMAC() {
        System.out.println("🔐 开始Blake2b MAC测试");
        
        // 1. 准备测试数据
        byte[] key = generateRandomBytes(32);
        byte[] message = "Authenticated message with Blake2b".getBytes();
        
        System.out.println("📝 消息: " + new String(message));
        System.out.println("🔑 密钥长度: " + key.length + " 字节");
        
        // 2. 计算MAC
        byte[] mac1 = mac(message, key);
        byte[] mac2 = mac(message, key);
        
        // 3. 验证一致性
        assertArrayEquals("相同密钥和消息应产生相同MAC", mac1, mac2);
        assertEquals("MAC长度应为32字节", 32, mac1.length);
        
        System.out.println("🔐 MAC长度: " + mac1.length + " 字节");
        System.out.println("✅ MAC一致性验证通过");
        
        // 4. 测试不同密钥
        byte[] differentKey = generateRandomBytes(32);
        byte[] differentMac = mac(message, differentKey);
        assertFalse("不同密钥应产生不同MAC", 
                   Arrays.equals(mac1, differentMac));
        
        System.out.println("✅ 不同密钥产生不同MAC");
        
        // 5. 测试不同消息
        byte[] differentMessage = "Different message".getBytes();
        byte[] differentMessageMac = mac(differentMessage, key);
        assertFalse("不同消息应产生不同MAC", 
                   Arrays.equals(mac1, differentMessageMac));
        
        System.out.println("✅ 不同消息产生不同MAC");
        System.out.println("✅ Blake2b MAC测试通过\n");
    }
    
    /**
     * 任务3.4：Blake2b参数化测试
     */
    @Test
    public void testBlake2bParameters() {
        System.out.println("⚙️ 开始Blake2b参数化测试");
        
        byte[] input = "Parameterized Blake2b test".getBytes();
        byte[] key = generateRandomBytes(32);
        byte[] salt = generateRandomBytes(16);
        byte[] personal = new byte[16];
        System.arraycopy("BriarApp".getBytes(), 0, personal, 0, Math.min("BriarApp".getBytes().length, 16));
        
        // 测试不同参数组合
        byte[] hash1 = hashWithParameters(input, null, null, null);
        byte[] hash2 = hashWithParameters(input, key, null, null);
        byte[] hash3 = hashWithParameters(input, key, salt, null);
        byte[] hash4 = hashWithParameters(input, key, salt, personal);
        
        // 验证不同参数产生不同结果
        assertFalse("不同参数应产生不同哈希", Arrays.equals(hash1, hash2));
        assertFalse("不同参数应产生不同哈希", Arrays.equals(hash2, hash3));
        assertFalse("不同参数应产生不同哈希", Arrays.equals(hash3, hash4));
        
        System.out.println("✅ 无参数哈希: " + bytesToHex(hash1, 8));
        System.out.println("✅ 密钥哈希: " + bytesToHex(hash2, 8));
        System.out.println("✅ 密钥+盐哈希: " + bytesToHex(hash3, 8));
        System.out.println("✅ 全参数哈希: " + bytesToHex(hash4, 8));
        System.out.println("✅ Blake2b参数化测试通过\n");
    }
    
    /**
     * 任务3.5：Blake2b vs 其他哈希函数比较
     */
    @Test
    public void testComparisonWithOtherHashes() {
        System.out.println("⚖️ 开始Blake2b与其他哈希函数比较");
        
        byte[] input = "Hash function comparison test".getBytes();
        
        // Blake2b哈希
        byte[] blake2bHash = hash(input);
        
        // 简单的SHA-256比较（如果可用）
        try {
            java.security.MessageDigest sha256 = java.security.MessageDigest.getInstance("SHA-256");
            byte[] sha256Hash = sha256.digest(input);
            
            System.out.println("📊 哈希长度比较:");
            System.out.println("  Blake2b: " + blake2bHash.length + " 字节");
            System.out.println("  SHA-256: " + sha256Hash.length + " 字节");
            
            // 验证不同算法产生不同结果
            assertFalse("不同算法应产生不同哈希", Arrays.equals(blake2bHash, sha256Hash));
            System.out.println("✅ 不同算法产生不同哈希值");
            
        } catch (Exception e) {
            System.out.println("⚠️ SHA-256不可用，跳过比较");
        }
        
        System.out.println("✅ 哈希函数比较测试完成\n");
    }
    
    /**
     * 任务3.6：Blake2b性能测试
     */
    @Test
    public void testPerformance() {
        System.out.println("⚡ 开始Blake2b性能测试");
        
        byte[] input = new byte[1024]; // 1KB数据
        Arrays.fill(input, (byte) 0x42);
        
        int iterations = 10000;
        
        // 测试哈希性能
        long hashStart = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            hash(input);
        }
        long hashEnd = System.currentTimeMillis();
        
        // 测试MAC性能
        byte[] key = generateRandomBytes(32);
        long macStart = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            mac(input, key);
        }
        long macEnd = System.currentTimeMillis();
        
        double hashTime = (hashEnd - hashStart) / (double) iterations;
        double macTime = (macEnd - macStart) / (double) iterations;
        
        System.out.println("📊 性能统计 (" + iterations + " 次操作, 1KB数据):");
        System.out.println("  哈希平均时间: " + String.format("%.3f", hashTime) + " ms");
        System.out.println("  MAC平均时间: " + String.format("%.3f", macTime) + " ms");
        System.out.println("✅ Blake2b性能测试完成\n");
    }
    
    // ========== 实现方法 ==========
    
    /**
     * 计算Blake2b哈希（256位输出）
     */
    private byte[] hash(byte[] input) {
        Blake2bDigest digest = new Blake2bDigest(256); // 256位输出
        digest.update(input, 0, input.length);
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    /**
     * 计算指定长度的Blake2b哈希
     */
    private byte[] hashWithLength(byte[] input, int outputLength) {
        Blake2bDigest digest = new Blake2bDigest(outputLength * 8); // 位数
        digest.update(input, 0, input.length);
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    /**
     * 计算Blake2b MAC
     */
    private byte[] mac(byte[] message, byte[] key) {
        Blake2bDigest mac = new Blake2bDigest(key, 32, null, null);
        mac.update(message, 0, message.length);
        byte[] output = new byte[mac.getDigestSize()];
        mac.doFinal(output, 0);
        return output;
    }
    
    /**
     * 使用完整参数的Blake2b哈希
     */
    private byte[] hashWithParameters(byte[] input, byte[] key, byte[] salt, byte[] personal) {
        Blake2bDigest digest = new Blake2bDigest(key, 32, salt, personal);
        digest.update(input, 0, input.length);
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    /**
     * 生成随机字节
     */
    private byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        new java.security.SecureRandom().nextBytes(bytes);
        return bytes;
    }
    
    /**
     * 字节数组转十六进制字符串（显示前n个字节）
     */
    private String bytesToHex(byte[] bytes, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(bytes.length, maxBytes);
        for (int i = 0; i < limit; i++) {
            sb.append(String.format("%02x", bytes[i]));
        }
        if (bytes.length > maxBytes) {
            sb.append("...");
        }
        return sb.toString();
    }
}
