-------------------------------------------------------------------------------
Test set: database.learning.DatabaseMigrationLearning
-------------------------------------------------------------------------------
Tests run: 5, Failures: 0, Errors: 4, Skipped: 0, Time elapsed: 0.306 s <<< FAILURE! - in database.learning.DatabaseMigrationLearning
database.learning.DatabaseMigrationLearning.testDatabaseMigration  Time elapsed: 0.138 s  <<< ERROR!
org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.migrateToVersion(DatabaseMigrationLearning.java:556)
	at database.learning.DatabaseMigrationLearning.testDatabaseMigration(DatabaseMigrationLearning.java:131)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)

database.learning.DatabaseMigrationLearning.testMigrationFailureAndRollback  Time elapsed: 0.022 s  <<< ERROR!
org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.migrateToVersion(DatabaseMigrationLearning.java:556)
	at database.learning.DatabaseMigrationLearning.testMigrationFailureAndRollback(DatabaseMigrationLearning.java:213)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)

database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance  Time elapsed: 0.061 s  <<< ERROR!
org.h2.jdbc.JdbcBatchUpdateException: 
Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 1 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000001' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1269)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.insertLargeTestData(DatabaseMigrationLearning.java:833)
	at database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance(DatabaseMigrationLearning.java:278)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 1 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000001' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:508)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.index.Index.getDuplicateKeyException(Index.java:525)
	at org.h2.mvstore.db.MVSecondaryIndex.checkUnique(MVSecondaryIndex.java:223)
	at org.h2.mvstore.db.MVSecondaryIndex.add(MVSecondaryIndex.java:184)
	at org.h2.mvstore.db.MVTable.addRow(MVTable.java:519)
	at org.h2.command.dml.Insert.insertRows(Insert.java:174)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:169)
	at org.h2.command.Command.executeUpdate(Command.java:252)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdateInternal(JdbcPreparedStatement.java:209)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchElement(JdbcPreparedStatement.java:1317)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1263)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.insertLargeTestData(DatabaseMigrationLearning.java:833)
	at database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance(DatabaseMigrationLearning.java:278)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 2 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000002' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 3 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000003' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 4 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000004' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 5 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000005' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 6 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000006' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 7 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000007' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 8 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000008' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 9 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000009' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 10 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 11 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 12 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 13 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 14 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 15 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 16 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000010' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 17 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000011' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 18 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000012' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 19 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000013' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 20 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000014' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 21 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000015' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 22 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000016' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 23 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000017' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 24 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000018' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 25 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000019' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 26 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 27 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 28 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 29 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 30 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 31 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 32 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000020' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 33 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000021' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 34 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000022' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 35 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000023' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 36 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000024' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 37 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000025' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 38 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000026' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 39 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000027' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 40 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000028' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 41 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000029' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 42 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 43 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 44 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 45 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 46 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 47 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 48 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000030' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 49 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000031' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 50 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000032' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 51 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000033' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 52 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000034' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 53 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000035' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 54 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000036' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 55 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000037' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 56 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000038' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 57 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000039' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 58 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 59 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 60 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 61 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 62 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 63 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 64 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000040' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 65 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000041' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 66 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000042' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 67 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000043' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 68 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000044' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 69 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000045' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 70 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000046' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 71 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000047' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 72 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000048' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 73 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000049' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 74 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 75 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 76 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 77 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 78 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 79 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 80 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000050' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 81 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000051' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 82 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000052' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 83 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000053' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 84 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000054' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 85 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000055' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 86 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000056' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 87 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000057' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 88 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000058' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 89 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000059' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 90 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 91 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 92 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 93 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 94 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 95 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 96 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000060' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 97 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000061' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 98 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000062' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 99 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000063' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 100 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000064' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 101 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000065' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
(truncated)

database.learning.DatabaseMigrationLearning.testConcurrentMigrationSafety  Time elapsed: 0.05 s  <<< ERROR!
org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning.testConcurrentMigrationSafety(DatabaseMigrationLearning.java:409)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)

