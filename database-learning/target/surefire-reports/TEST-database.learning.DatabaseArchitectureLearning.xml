<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="database.learning.DatabaseArchitectureLearning" time="1.83" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172603601_3.jar /Volumes/ExtendData/Code/github/briar/database-learning/target/surefire 2025-07-04T17-26-03_521-jvmRun1 surefire-20250704172603601_1tmp surefire_0-20250704172603601_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="DatabaseArchitectureLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172603601_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testConcurrencyAndLocking" classname="database.learning.DatabaseArchitectureLearning" time="0.428">
    <system-out><![CDATA[🔒 开始并发访问和锁机制测试
==================================================
📖 测试读写锁机制:
  执行顺序:
    WRITE_0_START
    WRITE_0_END
    READ_0_START
    READ_1_START
    READ_0_END
    READ_1_END
    WRITE_1_START
    WRITE_1_END
  ✅ 数据一致性验证成功
✅ 并发访问和锁机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testDatabaseInitialization" classname="database.learning.DatabaseArchitectureLearning" time="0.237">
    <system-out><![CDATA[🗄️ 开始数据库初始化测试
==================================================
🔑 生成数据库加密密钥

📊 初始化H2数据库:
  ✅ H2数据库初始化成功
  数据库路径: jdbc:h2:/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/briar-test-db/h2db;WRITE_DELAY=0
  是否重新打开: false
  数据库产品: H2
  数据库版本: 2.1.214 (2022-06-13)

🔄 测试数据库重新打开:
  ✅ H2数据库重新打开成功

📊 初始化HyperSQL数据库:
  ✅ HyperSQL数据库初始化成功
  数据库路径: *****************************************************************************************************************************************
  数据库产品: HSQL Database Engine
  数据库版本: 2.7.1
✅ 数据库初始化测试完成

]]></system-out>
  </testcase>
  <testcase name="testTransactionManagement" classname="database.learning.DatabaseArchitectureLearning" time="0.081">
    <system-out><![CDATA[🔄 开始事务管理机制测试
==================================================
✅ 测试成功事务:
  ✅ 插入设置记录成功
  ✅ 事务提交验证成功

❌ 测试事务回滚:
  📝 插入第二条记录
  ✅ 捕获到预期异常
  ✅ 事务回滚验证成功
✅ 事务管理机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testTableCreation" classname="database.learning.DatabaseArchitectureLearning" time="0.029">
    <system-out><![CDATA[🏗️ 开始数据库表结构创建测试
==================================================
📋 创建核心表结构:
  ✅ 表 SETTINGS 创建成功
  ✅ 表 LOCALAUTHORS 创建成功
  ✅ 表 CONTACTS 创建成功
  ✅ 表 GROUPS 创建成功
  ✅ 表 MESSAGES 创建成功
  ✅ 表 MESSAGEMETADATA 创建成功
  ✅ 表 STATUSES 创建成功

🔍 检查表结构:
    MESSAGEID: BINARY
    GROUPID: BINARY
    TIMESTAMP: BIGINT
    STATE: INTEGER
    SHARED: BOOLEAN
    TEMPORARY: BOOLEAN
    CLEANUPTIMERDURATION: BIGINT
    CLEANUPDEADLINE: BIGINT
    LENGTH: INTEGER
    RAW: BINARY LARGE OBJECT

🔗 检查外键约束:
    MESSAGES.GROUPID -> GROUPS.GROUPID
✅ 数据库表结构创建测试完成

]]></system-out>
  </testcase>
  <testcase name="testConnectionPoolManagement" classname="database.learning.DatabaseArchitectureLearning" time="1.036">
    <system-out><![CDATA[🏊 开始连接池管理测试
==================================================
🔧 连接池配置:
  最小连接数: 5
  最大连接数: 10

📊 测试连接获取和释放:
  ✅ 获取连接 1, 活跃连接数: 1
  ✅ 获取连接 2, 活跃连接数: 2
  ✅ 获取连接 3, 活跃连接数: 3
  ✅ 释放连接 1, 活跃连接数: 2
  ✅ 释放连接 2, 活跃连接数: 1
  ✅ 释放连接 3, 活跃连接数: 0

🚫 测试连接池限制:
  ✅ 达到最大连接数: 10
  ✅ 正确抛出连接池耗尽异常
✅ 连接池管理测试完成

]]></system-out>
  </testcase>
</testsuite>