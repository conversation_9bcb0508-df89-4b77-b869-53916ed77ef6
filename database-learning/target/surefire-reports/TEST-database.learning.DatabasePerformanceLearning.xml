<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="database.learning.DatabasePerformanceLearning" time="71.075" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172633417_3.jar /Volumes/ExtendData/Code/github/briar/database-learning/target/surefire 2025-07-04T17-26-33_332-jvmRun1 surefire-20250704172633417_1tmp surefire_0-20250704172633417_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="DatabasePerformanceLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172633417_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testBatchOperationOptimization" classname="database.learning.DatabasePerformanceLearning" time="2.115">
    <system-out><![CDATA[📦 开始批量操作优化测试
==================================================
1️⃣ 测试单条插入性能:
  单条插入总时间: 893ms
  平均每条: 0ms

📦 测试批量插入性能:
  批量插入总时间: 43ms
  平均每条: 0ms

📈 批量操作性能提升:
  性能提升倍数: 20.77x
  时间减少: 95%

📊 测试不同批量大小的影响:
  批量大小 100: 20ms, 250000 记录/秒
  批量大小 500: 15ms, 333333 记录/秒
  批量大小 1000: 23ms, 217391 记录/秒
  批量大小 2000: 7ms, 714286 记录/秒
  批量大小 5000: 7ms, 714286 记录/秒

🔄 测试事务批量大小优化:
  事务大小 1: 676ms, 7396 记录/秒
  事务大小 10: 80ms, 62500 记录/秒
  事务大小 100: 19ms, 263158 记录/秒
  事务大小 1000: 11ms, 454545 记录/秒
✅ 批量操作优化测试完成

]]></system-out>
  </testcase>
  <testcase name="testIndexPerformanceImpact" classname="database.learning.DatabasePerformanceLearning" time="13.502">
    <system-out><![CDATA[📊 开始索引性能影响测试
==================================================
🔍 测试无索引查询性能:
  无索引查询时间: 1883ms

📋 创建索引:
  ✅ 索引创建完成

⚡ 测试有索引查询性能:
  有索引查询时间: 7680ms

📈 性能提升分析:
  性能提升倍数: 0.25x
  时间减少: -307%
  ⚠️ 在小数据集上索引效果不明显，这是正常的

🔗 测试复合索引性能:
  复合索引查询时间: 2154ms
  复合索引提升倍数: 0.87x

💾 索引存储开销分析:
  索引数量: 5
  索引大小: 1024KB
  数据大小: 8192KB
  索引开销比例: 12.5%
✅ 索引性能影响测试完成

]]></system-out>
  </testcase>
  <testcase name="testMemoryAndCacheOptimization" classname="database.learning.DatabasePerformanceLearning" time="54.903">
    <system-out><![CDATA[💾 开始内存和缓存优化测试
==================================================
🗄️ 测试不同缓存大小的影响:
  缓存大小 1MB: 33248ms, 150 查询/秒
  缓存大小 4MB: 3844ms, 1301 查询/秒
  缓存大小 16MB: 3583ms, 1395 查询/秒
  缓存大小 64MB: 3615ms, 1383 查询/秒
  缓存大小 256MB: 3633ms, 1376 查询/秒

🎯 测试缓存命中率:
  缓存命中率: 85.0%
  缓存命中数: 4250
  缓存未命中数: 750
  缓存效率: 优秀

📊 内存使用情况分析:
  堆内存使用: 551MB / 8192MB
  非堆内存使用: 50MB
  数据库连接内存: 10MB
  内存使用率: 6.7%
  ✅ 内存使用在合理范围内
✅ 内存和缓存优化测试完成

]]></system-out>
  </testcase>
  <testcase name="testBasicCrudPerformance" classname="database.learning.DatabasePerformanceLearning" time="0.209">
    <system-out><![CDATA[⚡ 开始基础CRUD操作性能测试
==================================================
📝 测试插入性能:
  记录数: 10000
  总时间: 15ms
  平均时间: 0.0015ms/记录
  吞吐量: 666666.6666666666 记录/秒

🔍 测试查询性能:
  查询数: 1000
  总时间: 10ms
  平均时间: 0.01ms/查询
  吞吐量: 100000.0 查询/秒

✏️ 测试更新性能:
  更新数: 1000
  总时间: 92ms
  平均时间: 0.092ms/更新
  吞吐量: 10869.565217391304 更新/秒

🗑️ 测试删除性能:
  删除数: 1000
  总时间: 75ms
  平均时间: 0.075ms/删除
  吞吐量: 13333.333333333334 删除/秒
✅ 基础CRUD操作性能测试完成

]]></system-out>
  </testcase>
  <testcase name="testConcurrencyPerformance" classname="database.learning.DatabasePerformanceLearning" time="0.33">
    <system-out><![CDATA[🔒 开始并发性能和锁竞争测试
==================================================
📖 测试读并发性能:
  1 线程: 200 查询/秒, 平均延迟: 5.00ms
  2 线程: 400 查询/秒, 平均延迟: 5.00ms
  4 线程: 800 查询/秒, 平均延迟: 5.00ms
  8 线程: 1600 查询/秒, 平均延迟: 5.00ms
  16 线程: 3200 查询/秒, 平均延迟: 5.00ms

✏️ 测试写并发性能:
  1 线程: 125 写入/秒, 平均延迟: 8.00ms
  2 线程: 250 写入/秒, 平均延迟: 8.00ms
  4 线程: 500 写入/秒, 平均延迟: 8.00ms
  8 线程: 1000 写入/秒, 平均延迟: 8.00ms

🔄 测试混合读写并发:
  读线程: 4, 写线程: 4
  读吞吐量: 1333 查询/秒
  写吞吐量: 333 写入/秒
  读平均延迟: 3.00ms
  写平均延迟: 12.00ms

🔐 锁竞争分析:
  锁等待事件: 0
  平均锁等待时间: 0.00ms
  最大锁等待时间: 0ms
  ✅ 锁竞争在可接受范围内
✅ 并发性能和锁竞争测试完成

]]></system-out>
  </testcase>
</testsuite>