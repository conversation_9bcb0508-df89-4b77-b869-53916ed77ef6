<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="database.learning.DatabaseMigrationLearning" time="0.042" tests="5" errors="5" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704165041446_3.jar /Volumes/ExtendData/Code/github/briar/database-learning/target/surefire 2025-07-04T16-50-41_368-jvmRun1 surefire-20250704165041446_1tmp surefire_0-20250704165041446_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704165041446_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testDatabaseMigration" classname="database.learning.DatabaseMigrationLearning" time="0.001">
    <error message="Syntax error in SQL statement &quot;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))&quot;; expected &quot;identifier&quot;; SQL statement:&#10;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Syntax error in SQL statement "CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))"; expected "identifier"; SQL statement:
CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.getSyntaxError(DbException.java:261)
	at org.h2.command.Parser.readIdentifier(Parser.java:5656)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9326)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeUpdateInternal(JdbcStatement.java:186)
	at org.h2.jdbc.JdbcStatement.executeUpdate(JdbcStatement.java:143)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.createSchemaForVersion(DatabaseMigrationLearning.java:729)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.initializeDatabase(DatabaseMigrationLearning.java:498)
	at database.learning.DatabaseMigrationLearning.testDatabaseMigration(DatabaseMigrationLearning.java:109)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[🔄 开始数据库迁移流程测试
==================================================
📊 创建版本1数据库:
]]></system-out>
  </testcase>
  <testcase name="testMigrationFailureAndRollback" classname="database.learning.DatabaseMigrationLearning" time="0.01">
    <error message="Syntax error in SQL statement &quot;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))&quot;; expected &quot;identifier&quot;; SQL statement:&#10;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Syntax error in SQL statement "CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))"; expected "identifier"; SQL statement:
CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.getSyntaxError(DbException.java:261)
	at org.h2.command.Parser.readIdentifier(Parser.java:5656)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9326)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeUpdateInternal(JdbcStatement.java:186)
	at org.h2.jdbc.JdbcStatement.executeUpdate(JdbcStatement.java:143)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.createSchemaForVersion(DatabaseMigrationLearning.java:729)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.initializeDatabase(DatabaseMigrationLearning.java:498)
	at database.learning.DatabaseMigrationLearning.testMigrationFailureAndRollback(DatabaseMigrationLearning.java:185)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[❌ 开始迁移失败和回滚测试
==================================================
]]></system-out>
  </testcase>
  <testcase name="testDatabaseVersionManagement" classname="database.learning.DatabaseMigrationLearning" time="0.01">
    <error message="Syntax error in SQL statement &quot;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))&quot;; expected &quot;identifier&quot;; SQL statement:&#10;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Syntax error in SQL statement "CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))"; expected "identifier"; SQL statement:
CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.getSyntaxError(DbException.java:261)
	at org.h2.command.Parser.readIdentifier(Parser.java:5656)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9326)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeUpdateInternal(JdbcStatement.java:186)
	at org.h2.jdbc.JdbcStatement.executeUpdate(JdbcStatement.java:143)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.createSchemaForVersion(DatabaseMigrationLearning.java:729)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.initializeDatabase(DatabaseMigrationLearning.java:498)
	at database.learning.DatabaseMigrationLearning.testDatabaseVersionManagement(DatabaseMigrationLearning.java:62)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[📊 开始数据库版本管理测试
==================================================
🆕 初始化数据库版本1:
]]></system-out>
  </testcase>
  <testcase name="testLargeDataMigrationPerformance" classname="database.learning.DatabaseMigrationLearning" time="0.01">
    <error message="Syntax error in SQL statement &quot;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))&quot;; expected &quot;identifier&quot;; SQL statement:&#10;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Syntax error in SQL statement "CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))"; expected "identifier"; SQL statement:
CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.getSyntaxError(DbException.java:261)
	at org.h2.command.Parser.readIdentifier(Parser.java:5656)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9326)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeUpdateInternal(JdbcStatement.java:186)
	at org.h2.jdbc.JdbcStatement.executeUpdate(JdbcStatement.java:143)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.createSchemaForVersion(DatabaseMigrationLearning.java:729)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.initializeDatabase(DatabaseMigrationLearning.java:498)
	at database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance(DatabaseMigrationLearning.java:258)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[⚡ 开始大数据量迁移性能测试
==================================================
📊 创建大数据量数据库:
]]></system-out>
  </testcase>
  <testcase name="testConcurrentMigrationSafety" classname="database.learning.DatabaseMigrationLearning" time="0.011">
    <error message="Syntax error in SQL statement &quot;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))&quot;; expected &quot;identifier&quot;; SQL statement:&#10;CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Syntax error in SQL statement "CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  [*]value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey))"; expected "identifier"; SQL statement:
CREATE TABLE IF NOT EXISTS settings (  namespace VARCHAR(255) NOT NULL,  settingKey VARCHAR(255) NOT NULL,  value TEXT NOT NULL,  PRIMARY KEY (namespace, settingKey)) [42001-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.getSyntaxError(DbException.java:261)
	at org.h2.command.Parser.readIdentifier(Parser.java:5656)
	at org.h2.command.Parser.parseTableColumnDefinition(Parser.java:9326)
	at org.h2.command.Parser.parseCreateTable(Parser.java:9271)
	at org.h2.command.Parser.parseCreate(Parser.java:6784)
	at org.h2.command.Parser.parsePrepared(Parser.java:763)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcStatement.executeUpdateInternal(JdbcStatement.java:186)
	at org.h2.jdbc.JdbcStatement.executeUpdate(JdbcStatement.java:143)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.createSchemaForVersion(DatabaseMigrationLearning.java:729)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.initializeDatabase(DatabaseMigrationLearning.java:498)
	at database.learning.DatabaseMigrationLearning.testConcurrentMigrationSafety(DatabaseMigrationLearning.java:323)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[🔒 开始并发迁移安全性测试
==================================================
]]></system-out>
  </testcase>
</testsuite>