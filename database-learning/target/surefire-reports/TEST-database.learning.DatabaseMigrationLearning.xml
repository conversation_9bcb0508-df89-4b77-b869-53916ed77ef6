<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="database.learning.DatabaseMigrationLearning" time="0.306" tests="5" errors="4" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172754999_3.jar /Volumes/ExtendData/Code/github/briar/database-learning/target/surefire 2025-07-04T17-27-54_887-jvmRun1 surefire-20250704172754999_1tmp surefire_0-20250704172754999_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="DatabaseMigrationLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704172754999_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testDatabaseMigration" classname="database.learning.DatabaseMigrationLearning" time="0.138">
    <error message="Table &quot;SETTINGS&quot; not found (this database is empty); SQL statement:&#10;SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.migrateToVersion(DatabaseMigrationLearning.java:556)
	at database.learning.DatabaseMigrationLearning.testDatabaseMigration(DatabaseMigrationLearning.java:131)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[🔄 开始数据库迁移流程测试
==================================================
📊 创建版本1数据库:
  版本1数据统计:
    用户数: 5
    消息数: 10

🔄 迁移到版本2:
]]></system-out>
  </testcase>
  <testcase name="testMigrationFailureAndRollback" classname="database.learning.DatabaseMigrationLearning" time="0.022">
    <error message="Table &quot;SETTINGS&quot; not found (this database is empty); SQL statement:&#10;SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.migrateToVersion(DatabaseMigrationLearning.java:556)
	at database.learning.DatabaseMigrationLearning.testMigrationFailureAndRollback(DatabaseMigrationLearning.java:213)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[❌ 开始迁移失败和回滚测试
==================================================
📊 迁移前状态:
  版本: 1
  用户数: 5

💥 尝试失败的迁移:
]]></system-out>
  </testcase>
  <testcase name="testDatabaseVersionManagement" classname="database.learning.DatabaseMigrationLearning" time="0.02">
    <system-out><![CDATA[📊 开始数据库版本管理测试
==================================================
🆕 初始化数据库版本1:
  ✅ 当前数据库版本: 1
  📋 版本1表结构:
    - CONSTANTS
    - ENUM_VALUES
    - INDEXES
    - INDEX_COLUMNS
    - IN_DOUBT
    - LOCKS
    - QUERY_STATISTICS
    - RIGHTS
    - ROLES
    - SESSIONS
    - SESSION_STATE
    - SETTINGS
    - SYNONYMS
    - USERS
    - MESSAGES
    - SETTINGS
    - USERS
  📊 测试数据统计:
    用户数: 5
    消息数: 10
✅ 数据库版本管理测试完成

]]></system-out>
  </testcase>
  <testcase name="testLargeDataMigrationPerformance" classname="database.learning.DatabaseMigrationLearning" time="0.061">
    <error message="Unique index or primary key violation: &quot;PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 1 */ CAST(X&apos;0000000000000000000000000000000000000000000000000000000000000001&apos; AS BINARY(32)) )&quot;; SQL statement:&#10;INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]" type="org.h2.jdbc.JdbcBatchUpdateException"><![CDATA[org.h2.jdbc.JdbcBatchUpdateException: 
Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 1 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000001' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1269)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.insertLargeTestData(DatabaseMigrationLearning.java:833)
	at database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance(DatabaseMigrationLearning.java:278)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 1 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000001' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:508)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.index.Index.getDuplicateKeyException(Index.java:525)
	at org.h2.mvstore.db.MVSecondaryIndex.checkUnique(MVSecondaryIndex.java:223)
	at org.h2.mvstore.db.MVSecondaryIndex.add(MVSecondaryIndex.java:184)
	at org.h2.mvstore.db.MVTable.addRow(MVTable.java:519)
	at org.h2.command.dml.Insert.insertRows(Insert.java:174)
	at org.h2.command.dml.Insert.update(Insert.java:135)
	at org.h2.command.dml.DataChangeStatement.update(DataChangeStatement.java:74)
	at org.h2.command.CommandContainer.update(CommandContainer.java:169)
	at org.h2.command.Command.executeUpdate(Command.java:252)
	at org.h2.jdbc.JdbcPreparedStatement.executeUpdateInternal(JdbcPreparedStatement.java:209)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatchElement(JdbcPreparedStatement.java:1317)
	at org.h2.jdbc.JdbcPreparedStatement.executeBatch(JdbcPreparedStatement.java:1263)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.insertLargeTestData(DatabaseMigrationLearning.java:833)
	at database.learning.DatabaseMigrationLearning.testLargeDataMigrationPerformance(DatabaseMigrationLearning.java:278)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 2 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000002' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 3 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000003' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 4 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000004' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 5 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000005' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 6 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000006' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 7 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000007' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 8 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000008' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 9 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000009' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 10 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 11 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 12 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 13 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 14 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 15 */ CAST(X'000000000000000000000000000000000000000000000000000000000000000f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 16 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000010' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 17 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000011' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 18 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000012' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 19 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000013' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 20 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000014' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 21 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000015' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 22 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000016' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 23 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000017' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 24 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000018' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 25 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000019' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 26 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 27 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 28 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 29 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 30 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 31 */ CAST(X'000000000000000000000000000000000000000000000000000000000000001f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 32 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000020' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 33 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000021' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 34 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000022' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 35 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000023' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 36 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000024' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 37 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000025' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 38 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000026' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 39 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000027' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 40 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000028' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 41 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000029' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 42 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 43 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 44 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 45 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 46 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 47 */ CAST(X'000000000000000000000000000000000000000000000000000000000000002f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 48 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000030' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 49 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000031' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 50 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000032' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 51 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000033' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 52 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000034' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 53 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000035' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 54 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000036' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 55 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000037' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 56 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000038' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 57 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000039' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 58 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 59 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 60 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 61 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 62 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 63 */ CAST(X'000000000000000000000000000000000000000000000000000000000000003f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 64 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000040' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 65 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000041' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 66 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000042' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 67 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000043' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 68 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000044' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 69 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000045' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 70 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000046' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 71 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000047' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 72 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000048' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 73 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000049' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 74 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 75 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 76 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 77 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 78 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 79 */ CAST(X'000000000000000000000000000000000000000000000000000000000000004f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 80 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000050' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 81 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000051' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 82 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000052' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 83 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000053' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 84 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000054' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 85 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000055' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 86 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000056' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 87 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000057' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 88 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000058' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 89 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000059' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 90 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005a' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 91 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005b' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 92 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005c' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 93 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005d' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 94 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005e' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 95 */ CAST(X'000000000000000000000000000000000000000000000000000000000000005f' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 96 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000060' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 97 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000061' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 98 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000062' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 99 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000063' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 100 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000064' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException: Unique index or primary key violation: "PUBLIC.PRIMARY_KEY_4 ON PUBLIC.USERS(USERID) VALUES ( /* 101 */ CAST(X'0000000000000000000000000000000000000000000000000000000000000065' AS BINARY(32)) )"; SQL statement:
INSERT INTO users (userId, username, email, created) VALUES (?, ?, ?, ?) [23505-214]
(truncated)
]]></error>
    <system-out><![CDATA[⚡ 开始大数据量迁移性能测试
==================================================
📊 创建大数据量数据库:
]]></system-out>
  </testcase>
  <testcase name="testConcurrentMigrationSafety" classname="database.learning.DatabaseMigrationLearning" time="0.05">
    <error message="Table &quot;SETTINGS&quot; not found (this database is empty); SQL statement:&#10;SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]" type="org.h2.jdbc.JdbcSQLSyntaxErrorException"><![CDATA[org.h2.jdbc.JdbcSQLSyntaxErrorException: 
Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:502)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:477)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8385)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:8369)
	at org.h2.command.Parser.readTableOrView(Parser.java:8358)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1863)
	at org.h2.command.Parser.readTableReference(Parser.java:2334)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2772)
	at org.h2.command.Parser.parseSelect(Parser.java:2878)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2762)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2633)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2612)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2605)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2598)
	at org.h2.command.Parser.parseQuery(Parser.java:2567)
	at org.h2.command.Parser.parsePrepared(Parser.java:724)
	at org.h2.command.Parser.parse(Parser.java:689)
	at org.h2.command.Parser.parse(Parser.java:661)
	at org.h2.command.Parser.prepareCommand(Parser.java:569)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:631)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:554)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1116)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:92)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:288)
	at database.learning.DatabaseMigrationLearning$DatabaseMigrationManager.getCurrentSchemaVersion(DatabaseMigrationLearning.java:700)
	at database.learning.DatabaseMigrationLearning.testConcurrentMigrationSafety(DatabaseMigrationLearning.java:409)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:59)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:56)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.internal.runners.statements.RunAfters.evaluate(RunAfters.java:27)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:377)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:284)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:248)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:167)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
]]></error>
    <system-out><![CDATA[🔒 开始并发迁移安全性测试
==================================================
🔄 模拟并发迁移:
  任务 2 异常: Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
  任务 1 异常: Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]
  任务 0 异常: Table "SETTINGS" not found (this database is empty); SQL statement:
SELECT settingValue FROM settings WHERE namespace = ? AND settingKey = ? [42104-214]

📊 并发迁移结果分析:
  成功任务数: 0
  失败任务数: 0
  异常数: 3
]]></system-out>
  </testcase>
</testsuite>