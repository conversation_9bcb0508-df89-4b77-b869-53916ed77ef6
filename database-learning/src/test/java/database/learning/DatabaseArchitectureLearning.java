package database.learning;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.*;
import java.io.File;
import java.security.SecureRandom;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import static org.junit.Assert.*;

/**
 * 数据库架构学习实践
 * 
 * 学习目标：
 * 1. 理解Briar的数据库架构设计
 * 2. 掌握H2和HyperSQL数据库的使用
 * 3. 学习事务管理和连接池
 * 4. 实现数据库加密和安全机制
 */
public class DatabaseArchitectureLearning {
    
    private DatabaseManager databaseManager;
    private TransactionManager transactionManager;
    private ConnectionPoolManager connectionPool;
    private File testDbDir;
    
    @Before
    public void setUp() throws Exception {
        // 创建测试数据库目录
        testDbDir = new File(System.getProperty("java.io.tmpdir"), "briar-test-db");
        if (testDbDir.exists()) {
            deleteDirectory(testDbDir);
        }
        testDbDir.mkdirs();
        
        // 初始化数据库组件
        DatabaseConfig config = new DatabaseConfig(testDbDir);
        databaseManager = new DatabaseManager(config);
        // 注意：这里需要先初始化数据库才能创建事务管理器
        // transactionManager 和 connectionPool 将在具体测试中创建
    }
    
    @After
    public void tearDown() throws Exception {
        if (databaseManager != null) {
            databaseManager.close();
        }
        if (testDbDir != null && testDbDir.exists()) {
            deleteDirectory(testDbDir);
        }
    }
    
    /**
     * 测试1：数据库初始化和连接
     */
    @Test
    public void testDatabaseInitialization() throws Exception {
        System.out.println("🗄️ 开始数据库初始化测试");
        System.out.println("=" .repeat(50));
        
        // 生成数据库加密密钥
        SecretKey encryptionKey = generateEncryptionKey();
        System.out.println("🔑 生成数据库加密密钥");

        // 初始化H2数据库
        System.out.println("\n📊 初始化H2数据库:");

        boolean h2Reopened = databaseManager.open(encryptionKey, DatabaseType.H2);
        assertFalse("首次打开应该返回false", h2Reopened);

        System.out.println("  ✅ H2数据库初始化成功");
        System.out.println("  数据库路径: " + databaseManager.getDatabasePath());
        System.out.println("  是否重新打开: " + h2Reopened);

        // 测试连接
        Connection h2Connection = databaseManager.createConnection();
        assertNotNull("H2连接不应为空", h2Connection);
        assertFalse("连接应该禁用自动提交", h2Connection.getAutoCommit());

        DatabaseMetaData h2MetaData = h2Connection.getMetaData();
        System.out.println("  数据库产品: " + h2MetaData.getDatabaseProductName());
        System.out.println("  数据库版本: " + h2MetaData.getDatabaseProductVersion());

        h2Connection.close();
        databaseManager.close();
        
        // 测试重新打开
        System.out.println("\n🔄 测试数据库重新打开:");

        databaseManager = new DatabaseManager(new DatabaseConfig(testDbDir));
        boolean h2ReopenedAgain = databaseManager.open(encryptionKey, DatabaseType.H2);
        assertTrue("重新打开应该返回true", h2ReopenedAgain);
        System.out.println("  ✅ H2数据库重新打开成功");

        databaseManager.close();

        // 初始化HyperSQL数据库
        System.out.println("\n📊 初始化HyperSQL数据库:");

        // 为HyperSQL使用不同的目录
        File hsqlDbDir = new File(testDbDir, "hsql");
        hsqlDbDir.mkdirs();
        databaseManager = new DatabaseManager(new DatabaseConfig(hsqlDbDir));
        boolean hsqlReopened = databaseManager.open(encryptionKey, DatabaseType.HYPERSQL);
        assertFalse("首次打开应该返回false", hsqlReopened);

        System.out.println("  ✅ HyperSQL数据库初始化成功");
        System.out.println("  数据库路径: " + databaseManager.getDatabasePath());

        Connection hsqlConnection = databaseManager.createConnection();
        assertNotNull("HyperSQL连接不应为空", hsqlConnection);

        DatabaseMetaData hsqlMetaData = hsqlConnection.getMetaData();
        System.out.println("  数据库产品: " + hsqlMetaData.getDatabaseProductName());
        System.out.println("  数据库版本: " + hsqlMetaData.getDatabaseProductVersion());

        hsqlConnection.close();
        databaseManager.close();
        
        System.out.println("✅ 数据库初始化测试完成\n");
    }
    
    /**
     * 测试2：数据库表结构创建
     */
    @Test
    public void testTableCreation() throws Exception {
        System.out.println("🏗️ 开始数据库表结构创建测试");
        System.out.println("=" .repeat(50));
        
        SecretKey encryptionKey = generateEncryptionKey();
        databaseManager = new DatabaseManager(new DatabaseConfig(testDbDir));
        databaseManager.open(encryptionKey, DatabaseType.H2);

        Connection connection = databaseManager.createConnection();
        
        // 创建核心表结构
        System.out.println("📋 创建核心表结构:");
        
        SchemaManager schemaManager = new SchemaManager();
        schemaManager.createTables(connection);
        
        // 验证表是否创建成功
        DatabaseMetaData metaData = connection.getMetaData();
        
        String[] expectedTables = {
            "SETTINGS", "LOCAL_AUTHORS", "CONTACTS", "GROUPS", 
            "MESSAGES", "MESSAGE_METADATA", "STATUSES"
        };
        
        for (String tableName : expectedTables) {
            ResultSet tables = metaData.getTables(null, null, tableName, null);
            assertTrue("表 " + tableName + " 应该存在", tables.next());
            System.out.println("  ✅ 表 " + tableName + " 创建成功");
            tables.close();
        }
        
        // 检查表结构
        System.out.println("\n🔍 检查表结构:");
        
        // 检查MESSAGES表的列
        ResultSet columns = metaData.getColumns(null, null, "MESSAGES", null);
        Set<String> messageColumns = new HashSet<>();
        while (columns.next()) {
            String columnName = columns.getString("COLUMN_NAME");
            String columnType = columns.getString("TYPE_NAME");
            messageColumns.add(columnName);
            System.out.println("    " + columnName + ": " + columnType);
        }
        columns.close();
        
        // 验证必要的列存在
        String[] expectedColumns = {
            "MESSAGEID", "GROUPID", "TIMESTAMP", "STATE", 
            "SHARED", "TEMPORARY", "LENGTH", "RAW"
        };
        
        for (String column : expectedColumns) {
            assertTrue("MESSAGES表应该包含列 " + column, messageColumns.contains(column));
        }
        
        // 检查外键约束
        System.out.println("\n🔗 检查外键约束:");
        
        ResultSet foreignKeys = metaData.getImportedKeys(null, null, "MESSAGES");
        boolean hasForeignKey = false;
        while (foreignKeys.next()) {
            String fkTable = foreignKeys.getString("FKTABLE_NAME");
            String fkColumn = foreignKeys.getString("FKCOLUMN_NAME");
            String pkTable = foreignKeys.getString("PKTABLE_NAME");
            String pkColumn = foreignKeys.getString("PKCOLUMN_NAME");
            
            System.out.println("    " + fkTable + "." + fkColumn + " -> " + pkTable + "." + pkColumn);
            hasForeignKey = true;
        }
        foreignKeys.close();
        
        assertTrue("MESSAGES表应该有外键约束", hasForeignKey);
        
        connection.close();
        databaseManager.close();
        
        System.out.println("✅ 数据库表结构创建测试完成\n");
    }
    
    /**
     * 测试3：事务管理机制
     */
    @Test
    public void testTransactionManagement() throws Exception {
        System.out.println("🔄 开始事务管理机制测试");
        System.out.println("=" .repeat(50));
        
        SecretKey encryptionKey = generateEncryptionKey();
        databaseManager = new DatabaseManager(new DatabaseConfig(testDbDir));
        databaseManager.open(encryptionKey, DatabaseType.H2);

        Connection connection = databaseManager.createConnection();
        SchemaManager schemaManager = new SchemaManager();
        schemaManager.createTables(connection);
        connection.close();

        // 创建一个简单的数据库包装器用于事务管理器
        AbstractDatabase dbWrapper = new AbstractDatabase(testDbDir) {
            @Override
            public boolean open(SecretKey key) throws SQLException {
                return databaseManager.open(key, DatabaseType.H2);
            }

            @Override
            public void close() throws SQLException {
                databaseManager.close();
            }

            @Override
            public Connection createConnection() throws SQLException {
                return databaseManager.createConnection();
            }

            @Override
            public String getDatabasePath() {
                return databaseManager.getDatabasePath();
            }
        };

        TransactionManager txnManager = new TransactionManager(dbWrapper);
        
        // 测试成功事务
        System.out.println("✅ 测试成功事务:");
        
        txnManager.transaction(false, txn -> {
            PreparedStatement ps = txn.prepareStatement(
                "INSERT INTO settings (namespace, settingKey, value) VALUES (?, ?, ?)");
            ps.setString(1, "test");
            ps.setString(2, "key1");
            ps.setString(3, "value1");
            int result = ps.executeUpdate();
            assertEquals("应该插入1行", 1, result);
            ps.close();
            
            System.out.println("  ✅ 插入设置记录成功");
        });
        
        // 验证数据已提交
        String value = txnManager.transactionWithResult(true, txn -> {
            PreparedStatement ps = txn.prepareStatement(
                "SELECT value FROM settings WHERE namespace = ? AND settingKey = ?");
            ps.setString(1, "test");
            ps.setString(2, "key1");
            ResultSet rs = ps.executeQuery();
            String result = rs.next() ? rs.getString(1) : null;
            rs.close();
            ps.close();
            return result;
        });
        
        assertEquals("应该能查询到插入的值", "value1", value);
        System.out.println("  ✅ 事务提交验证成功");
        
        // 测试事务回滚
        System.out.println("\n❌ 测试事务回滚:");
        
        try {
            txnManager.transaction(false, txn -> {
                PreparedStatement ps = txn.prepareStatement(
                    "INSERT INTO settings (namespace, settingKey, value) VALUES (?, ?, ?)");
                ps.setString(1, "test");
                ps.setString(2, "key2");
                ps.setString(3, "value2");
                ps.executeUpdate();
                ps.close();
                
                System.out.println("  📝 插入第二条记录");
                
                // 故意抛出异常触发回滚
                throw new RuntimeException("模拟事务失败");
            });
            fail("应该抛出异常");
        } catch (RuntimeException e) {
            assertEquals("模拟事务失败", e.getMessage());
            System.out.println("  ✅ 捕获到预期异常");
        }
        
        // 验证回滚生效
        String rolledBackValue = txnManager.transactionWithResult(true, txn -> {
            PreparedStatement ps = txn.prepareStatement(
                "SELECT value FROM settings WHERE namespace = ? AND settingKey = ?");
            ps.setString(1, "test");
            ps.setString(2, "key2");
            ResultSet rs = ps.executeQuery();
            String result = rs.next() ? rs.getString(1) : null;
            rs.close();
            ps.close();
            return result;
        });
        
        assertNull("回滚后应该查询不到数据", rolledBackValue);
        System.out.println("  ✅ 事务回滚验证成功");
        
        databaseManager.close();
        
        System.out.println("✅ 事务管理机制测试完成\n");
    }
    
    /**
     * 测试4：并发访问和锁机制
     */
    @Test
    public void testConcurrencyAndLocking() throws Exception {
        System.out.println("🔒 开始并发访问和锁机制测试");
        System.out.println("=" .repeat(50));
        
        SecretKey encryptionKey = generateEncryptionKey();
        databaseManager = new DatabaseManager(new DatabaseConfig(testDbDir));
        databaseManager.open(encryptionKey, DatabaseType.H2);

        Connection connection = databaseManager.createConnection();
        SchemaManager schemaManager = new SchemaManager();
        schemaManager.createTables(connection);
        connection.close();

        // 创建数据库包装器
        AbstractDatabase dbWrapper = new AbstractDatabase(testDbDir) {
            @Override
            public boolean open(SecretKey key) throws SQLException {
                return databaseManager.open(key, DatabaseType.H2);
            }

            @Override
            public void close() throws SQLException {
                databaseManager.close();
            }

            @Override
            public Connection createConnection() throws SQLException {
                return databaseManager.createConnection();
            }

            @Override
            public String getDatabasePath() {
                return databaseManager.getDatabasePath();
            }
        };

        ConcurrentTransactionManager concurrentTxnManager = new ConcurrentTransactionManager(dbWrapper);
        
        // 测试读写锁
        System.out.println("📖 测试读写锁机制:");
        
        ExecutorService executor = Executors.newFixedThreadPool(4);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completeLatch = new CountDownLatch(4);
        List<String> executionOrder = Collections.synchronizedList(new ArrayList<>());
        
        // 启动多个读事务
        for (int i = 0; i < 2; i++) {
            final int readerId = i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    concurrentTxnManager.transaction(true, txn -> {
                        executionOrder.add("READ_" + readerId + "_START");
                        Thread.sleep(100);  // 模拟读操作
                        executionOrder.add("READ_" + readerId + "_END");
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        // 启动写事务
        for (int i = 0; i < 2; i++) {
            final int writerId = i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    concurrentTxnManager.transaction(false, txn -> {
                        executionOrder.add("WRITE_" + writerId + "_START");
                        PreparedStatement ps = txn.prepareStatement(
                            "INSERT INTO settings (namespace, settingKey, value) VALUES (?, ?, ?)");
                        ps.setString(1, "concurrent");
                        ps.setString(2, "writer_" + writerId);
                        ps.setString(3, "value_" + writerId);
                        ps.executeUpdate();
                        ps.close();
                        Thread.sleep(50);  // 模拟写操作
                        executionOrder.add("WRITE_" + writerId + "_END");
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    completeLatch.countDown();
                }
            });
        }
        
        // 开始执行
        startLatch.countDown();
        boolean completed = completeLatch.await(5, TimeUnit.SECONDS);
        assertTrue("所有事务应该在5秒内完成", completed);
        
        executor.shutdown();
        
        System.out.println("  执行顺序:");
        for (String event : executionOrder) {
            System.out.println("    " + event);
        }
        
        // 验证数据一致性
        int writerCount = concurrentTxnManager.transactionWithResult(true, txn -> {
            PreparedStatement ps = txn.prepareStatement(
                "SELECT COUNT(*) FROM settings WHERE namespace = ?");
            ps.setString(1, "concurrent");
            ResultSet rs = ps.executeQuery();
            int count = rs.next() ? rs.getInt(1) : 0;
            rs.close();
            ps.close();
            return count;
        });
        
        assertEquals("应该有2个写入记录", 2, writerCount);
        System.out.println("  ✅ 数据一致性验证成功");
        
        databaseManager.close();
        
        System.out.println("✅ 并发访问和锁机制测试完成\n");
    }
    
    /**
     * 测试5：连接池管理
     */
    @Test
    public void testConnectionPoolManagement() throws Exception {
        System.out.println("🏊 开始连接池管理测试");
        System.out.println("=" .repeat(50));
        
        SecretKey encryptionKey = generateEncryptionKey();
        databaseManager = new DatabaseManager(new DatabaseConfig(testDbDir));
        databaseManager.open(encryptionKey, DatabaseType.H2);

        // 创建数据库包装器
        AbstractDatabase dbWrapper = new AbstractDatabase(testDbDir) {
            @Override
            public boolean open(SecretKey key) throws SQLException {
                return databaseManager.open(key, DatabaseType.H2);
            }

            @Override
            public void close() throws SQLException {
                databaseManager.close();
            }

            @Override
            public Connection createConnection() throws SQLException {
                return databaseManager.createConnection();
            }

            @Override
            public String getDatabasePath() {
                return databaseManager.getDatabasePath();
            }
        };

        ConnectionPoolManager poolManager = new ConnectionPoolManager(dbWrapper, 5, 10);
        
        System.out.println("🔧 连接池配置:");
        System.out.println("  最小连接数: " + poolManager.getMinConnections());
        System.out.println("  最大连接数: " + poolManager.getMaxConnections());
        
        // 测试连接获取和释放
        System.out.println("\n📊 测试连接获取和释放:");
        
        List<Connection> connections = new ArrayList<>();
        
        // 获取多个连接
        for (int i = 0; i < 3; i++) {
            Connection conn = poolManager.getConnection();
            assertNotNull("连接不应为空", conn);
            assertFalse("连接应该禁用自动提交", conn.getAutoCommit());
            connections.add(conn);
            System.out.println("  ✅ 获取连接 " + (i + 1) + ", 活跃连接数: " + poolManager.getActiveConnections());
        }
        
        assertEquals("活跃连接数应该为3", 3, poolManager.getActiveConnections());
        
        // 释放连接
        for (int i = 0; i < connections.size(); i++) {
            poolManager.releaseConnection(connections.get(i));
            System.out.println("  ✅ 释放连接 " + (i + 1) + ", 活跃连接数: " + poolManager.getActiveConnections());
        }
        
        assertEquals("活跃连接数应该为0", 0, poolManager.getActiveConnections());
        
        // 测试连接池限制
        System.out.println("\n🚫 测试连接池限制:");
        
        List<Connection> maxConnections = new ArrayList<>();
        
        // 获取最大数量的连接
        for (int i = 0; i < poolManager.getMaxConnections(); i++) {
            Connection conn = poolManager.getConnection();
            maxConnections.add(conn);
        }
        
        assertEquals("应该达到最大连接数", poolManager.getMaxConnections(), poolManager.getActiveConnections());
        System.out.println("  ✅ 达到最大连接数: " + poolManager.getActiveConnections());
        
        // 尝试获取超出限制的连接
        try {
            Connection extraConn = poolManager.getConnection(1000); // 1秒超时
            fail("应该抛出超时异常");
        } catch (SQLException e) {
            System.out.println("  ✅ 正确抛出连接池耗尽异常");
        }
        
        // 释放所有连接
        for (Connection conn : maxConnections) {
            poolManager.releaseConnection(conn);
        }
        
        assertEquals("所有连接应该被释放", 0, poolManager.getActiveConnections());
        
        poolManager.close();
        databaseManager.close();
        
        System.out.println("✅ 连接池管理测试完成\n");
    }
    
    // ========== 辅助方法 ==========
    
    private SecretKey generateEncryptionKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256);
        return keyGen.generateKey();
    }
    
    private void deleteDirectory(File dir) {
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        dir.delete();
    }

    // ========== 数据库实现类 ==========

    /**
     * 数据库配置
     */
    static class DatabaseConfig {
        private final File databaseDirectory;

        public DatabaseConfig(File databaseDirectory) {
            this.databaseDirectory = databaseDirectory;
        }

        public File getDatabaseDirectory() {
            return databaseDirectory;
        }
    }

    /**
     * 抽象数据库基类
     */
    abstract static class AbstractDatabase {
        protected final File databaseDirectory;
        protected volatile boolean closed = false;

        public AbstractDatabase(File databaseDirectory) {
            this.databaseDirectory = databaseDirectory;
        }

        public abstract boolean open(SecretKey key) throws SQLException;
        public abstract void close() throws SQLException;
        public abstract Connection createConnection() throws SQLException;
        public abstract String getDatabasePath();

        protected boolean isNonEmptyDirectory(File dir) {
            if (!dir.exists() || !dir.isDirectory()) return false;
            File[] files = dir.listFiles();
            return files != null && files.length > 0;
        }
    }

    /**
     * H2数据库实现
     */
    static class H2DatabaseImpl extends AbstractDatabase {
        private static final String DRIVER_CLASS = "org.h2.Driver";
        private final String url;
        private volatile SecretKey encryptionKey;

        public H2DatabaseImpl(File databaseDirectory) {
            super(databaseDirectory);
            String path = new File(databaseDirectory, "h2db").getAbsolutePath();
            this.url = "jdbc:h2:" + path + ";CIPHER=AES;WRITE_DELAY=0";
        }

        @Override
        public boolean open(SecretKey key) throws SQLException {
            this.encryptionKey = key;
            boolean reopen = isNonEmptyDirectory(databaseDirectory);

            if (!reopen) {
                databaseDirectory.mkdirs(); // 创建目录，不检查返回值
            }

            // 加载H2驱动
            try {
                Class.forName(DRIVER_CLASS);
            } catch (ClassNotFoundException e) {
                throw new SQLException("无法加载H2驱动", e);
            }

            // 测试连接
            Connection testConn = createConnection();
            testConn.close();

            closed = false;
            return reopen;
        }

        @Override
        public void close() throws SQLException {
            closed = true;
            encryptionKey = null;
        }

        @Override
        public Connection createConnection() throws SQLException {
            if (closed || encryptionKey == null) {
                throw new SQLException("数据库已关闭");
            }

            Properties props = new Properties();
            props.setProperty("user", "user");
            // H2的加密格式：文件密码 + 空格 + 用户密码
            String hex = bytesToHex(encryptionKey.getEncoded());
            props.put("password", hex + " password");

            Connection conn = DriverManager.getConnection(url, props);
            conn.setAutoCommit(false);
            return conn;
        }

        @Override
        public String getDatabasePath() {
            return url;
        }

        private String bytesToHex(byte[] bytes) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
    }

    /**
     * HyperSQL数据库实现
     */
    static class HyperSqlDatabaseImpl extends AbstractDatabase {
        private static final String DRIVER_CLASS = "org.hsqldb.jdbc.JDBCDriver";
        private final String url;
        private volatile SecretKey encryptionKey;

        public HyperSqlDatabaseImpl(File databaseDirectory) {
            super(databaseDirectory);
            String path = new File(databaseDirectory, "hsqldb").getAbsolutePath();
            this.url = "jdbc:hsqldb:file:" + path
                    + ";sql.enforce_size=false;allow_empty_batch=true"
                    + ";encrypt_lobs=true;crypt_type=AES";
        }

        @Override
        public boolean open(SecretKey key) throws SQLException {
            this.encryptionKey = key;
            boolean reopen = isNonEmptyDirectory(databaseDirectory);

            if (!reopen) {
                databaseDirectory.mkdirs(); // 创建目录，不检查返回值
            }

            // 加载HyperSQL驱动
            try {
                Class.forName(DRIVER_CLASS);
            } catch (ClassNotFoundException e) {
                throw new SQLException("无法加载HyperSQL驱动", e);
            }

            // 测试连接
            Connection testConn = createConnection();
            testConn.close();

            closed = false;
            return reopen;
        }

        @Override
        public void close() throws SQLException {
            if (closed) return;

            // HyperSQL需要优雅关闭
            try (Connection conn = createConnection();
                 Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SHUTDOWN COMPACT");
            } catch (SQLException e) {
                // 忽略关闭时的异常
            }

            closed = true;
            encryptionKey = null;
        }

        @Override
        public Connection createConnection() throws SQLException {
            if (closed || encryptionKey == null) {
                throw new SQLException("数据库已关闭");
            }

            String hex = bytesToHex(encryptionKey.getEncoded());
            String connectionUrl = url + ";crypt_key=" + hex;

            Connection conn = DriverManager.getConnection(connectionUrl);
            conn.setAutoCommit(false);
            return conn;
        }

        @Override
        public String getDatabasePath() {
            return url;
        }

        private String bytesToHex(byte[] bytes) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }
    }

    /**
     * 数据库管理器
     */
    static class DatabaseManager {
        private final DatabaseConfig config;
        private AbstractDatabase database;

        public DatabaseManager(DatabaseConfig config) {
            this.config = config;
        }

        public boolean open(SecretKey key, DatabaseType type) throws SQLException {
            switch (type) {
                case H2:
                    database = new H2DatabaseImpl(config.getDatabaseDirectory());
                    break;
                case HYPERSQL:
                    database = new HyperSqlDatabaseImpl(config.getDatabaseDirectory());
                    break;
                default:
                    throw new IllegalArgumentException("不支持的数据库类型: " + type);
            }

            return database.open(key);
        }

        public void close() throws SQLException {
            if (database != null) {
                database.close();
            }
        }

        public Connection createConnection() throws SQLException {
            if (database == null) {
                throw new SQLException("数据库未初始化");
            }
            return database.createConnection();
        }

        public String getDatabasePath() {
            return database != null ? database.getDatabasePath() : null;
        }
    }

    /**
     * 数据库类型枚举
     */
    enum DatabaseType {
        H2, HYPERSQL
    }

    /**
     * 事务管理器
     */
    static class TransactionManager {
        private final AbstractDatabase database;

        public TransactionManager(AbstractDatabase database) {
            this.database = database;
        }

        public <E extends Exception> void transaction(boolean readOnly, DbRunnable<E> task)
                throws SQLException, E {
            Connection txn = database.createConnection();
            try {
                if (readOnly) {
                    txn.setReadOnly(true);
                }

                task.run(txn);
                txn.commit();
            } catch (Exception e) {
                try {
                    txn.rollback();
                } catch (SQLException rollbackEx) {
                    e.addSuppressed(rollbackEx);
                }
                throw e;
            } finally {
                try {
                    txn.close();
                } catch (SQLException e) {
                    // 记录但不抛出关闭异常
                    System.err.println("关闭连接时发生异常: " + e.getMessage());
                }
            }
        }

        public <R, E extends Exception> R transactionWithResult(boolean readOnly,
                DbCallable<R, E> task) throws SQLException, E {
            Connection txn = database.createConnection();
            try {
                if (readOnly) {
                    txn.setReadOnly(true);
                }

                R result = task.call(txn);
                txn.commit();
                return result;
            } catch (Exception e) {
                try {
                    txn.rollback();
                } catch (SQLException rollbackEx) {
                    e.addSuppressed(rollbackEx);
                }
                throw e;
            } finally {
                try {
                    txn.close();
                } catch (SQLException e) {
                    System.err.println("关闭连接时发生异常: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 并发事务管理器（支持读写锁）
     */
    static class ConcurrentTransactionManager {
        private final AbstractDatabase database;
        private final ReadWriteLock lock = new ReentrantReadWriteLock();

        public ConcurrentTransactionManager(AbstractDatabase database) {
            this.database = database;
        }

        public <E extends Exception> void transaction(boolean readOnly, DbRunnable<E> task)
                throws SQLException, E {
            Lock txnLock = readOnly ? lock.readLock() : lock.writeLock();
            txnLock.lock();

            try {
                Connection txn = database.createConnection();
                try {
                    if (readOnly) {
                        txn.setReadOnly(true);
                    }

                    task.run(txn);
                    txn.commit();
                } catch (Exception e) {
                    try {
                        txn.rollback();
                    } catch (SQLException rollbackEx) {
                        e.addSuppressed(rollbackEx);
                    }
                    throw e;
                } finally {
                    try {
                        txn.close();
                    } catch (SQLException e) {
                        System.err.println("关闭连接时发生异常: " + e.getMessage());
                    }
                }
            } finally {
                txnLock.unlock();
            }
        }

        public <R, E extends Exception> R transactionWithResult(boolean readOnly,
                DbCallable<R, E> task) throws SQLException, E {
            Lock txnLock = readOnly ? lock.readLock() : lock.writeLock();
            txnLock.lock();

            try {
                Connection txn = database.createConnection();
                try {
                    if (readOnly) {
                        txn.setReadOnly(true);
                    }

                    R result = task.call(txn);
                    txn.commit();
                    return result;
                } catch (Exception e) {
                    try {
                        txn.rollback();
                    } catch (SQLException rollbackEx) {
                        e.addSuppressed(rollbackEx);
                    }
                    throw e;
                } finally {
                    try {
                        txn.close();
                    } catch (SQLException e) {
                        System.err.println("关闭连接时发生异常: " + e.getMessage());
                    }
                }
            } finally {
                txnLock.unlock();
            }
        }
    }

    /**
     * 连接池管理器
     */
    static class ConnectionPoolManager {
        private final AbstractDatabase database;
        private final Queue<Connection> availableConnections = new ConcurrentLinkedQueue<>();
        private final Set<Connection> activeConnections = ConcurrentHashMap.newKeySet();
        private final int minConnections;
        private final int maxConnections;
        private volatile boolean closed = false;

        public ConnectionPoolManager(AbstractDatabase database, int minConnections, int maxConnections) {
            this.database = database;
            this.minConnections = minConnections;
            this.maxConnections = maxConnections;
        }

        public Connection getConnection() throws SQLException {
            return getConnection(0); // 无限等待
        }

        public Connection getConnection(long timeoutMs) throws SQLException {
            if (closed) {
                throw new SQLException("连接池已关闭");
            }

            // 尝试从池中获取连接
            Connection conn = availableConnections.poll();
            if (conn != null && !conn.isClosed()) {
                activeConnections.add(conn);
                return conn;
            }

            // 如果没有可用连接且未达到最大连接数，创建新连接
            if (activeConnections.size() < maxConnections) {
                conn = database.createConnection();
                activeConnections.add(conn);
                return conn;
            }

            // 等待连接可用
            if (timeoutMs > 0) {
                long startTime = System.currentTimeMillis();
                while (System.currentTimeMillis() - startTime < timeoutMs) {
                    conn = availableConnections.poll();
                    if (conn != null && !conn.isClosed()) {
                        activeConnections.add(conn);
                        return conn;
                    }

                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new SQLException("等待连接时被中断", e);
                    }
                }
                throw new SQLException("获取连接超时");
            }

            throw new SQLException("连接池已耗尽");
        }

        public void releaseConnection(Connection conn) throws SQLException {
            if (conn == null || conn.isClosed()) {
                return;
            }

            activeConnections.remove(conn);

            // 重置连接状态
            conn.rollback();
            conn.setReadOnly(false);
            conn.setAutoCommit(false);

            availableConnections.offer(conn);
        }

        public int getActiveConnections() {
            return activeConnections.size();
        }

        public int getAvailableConnections() {
            return availableConnections.size();
        }

        public int getMinConnections() {
            return minConnections;
        }

        public int getMaxConnections() {
            return maxConnections;
        }

        public void close() throws SQLException {
            closed = true;

            // 关闭所有连接
            for (Connection conn : activeConnections) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("关闭活跃连接时发生异常: " + e.getMessage());
                }
            }

            Connection conn;
            while ((conn = availableConnections.poll()) != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.err.println("关闭可用连接时发生异常: " + e.getMessage());
                }
            }

            activeConnections.clear();
        }
    }

    /**
     * 数据库运行接口
     */
    @FunctionalInterface
    interface DbRunnable<E extends Exception> {
        void run(Connection txn) throws SQLException, E;
    }

    /**
     * 数据库调用接口
     */
    @FunctionalInterface
    interface DbCallable<R, E extends Exception> {
        R call(Connection txn) throws SQLException, E;
    }

    /**
     * 数据库模式管理器
     */
    static class SchemaManager {

        // 数据类型定义
        private static final String HASH_TYPE = "BINARY(32)";
        private static final String SECRET_TYPE = "BINARY(32)";
        private static final String BINARY_TYPE = "BLOB";
        private static final String COUNTER_TYPE = "INT NOT NULL AUTO_INCREMENT";
        private static final String STRING_TYPE = "VARCHAR(255)";

        // 表创建SQL
        private static final String CREATE_SETTINGS =
                "CREATE TABLE settings (" +
                "  namespace " + STRING_TYPE + " NOT NULL," +
                "  settingKey " + STRING_TYPE + " NOT NULL," +
                "  value " + STRING_TYPE + " NOT NULL," +
                "  PRIMARY KEY (namespace, settingKey)" +
                ")";

        private static final String CREATE_LOCAL_AUTHORS =
                "CREATE TABLE localAuthors (" +
                "  authorId " + HASH_TYPE + " NOT NULL," +
                "  formatVersion INT NOT NULL," +
                "  name " + STRING_TYPE + " NOT NULL," +
                "  publicKey " + BINARY_TYPE + " NOT NULL," +
                "  privateKey " + BINARY_TYPE + " NOT NULL," +
                "  created BIGINT NOT NULL," +
                "  PRIMARY KEY (authorId)" +
                ")";

        private static final String CREATE_CONTACTS =
                "CREATE TABLE contacts (" +
                "  contactId " + COUNTER_TYPE + "," +
                "  authorId " + HASH_TYPE + " NOT NULL," +
                "  formatVersion INT NOT NULL," +
                "  name " + STRING_TYPE + " NOT NULL," +
                "  alias " + STRING_TYPE + "," +
                "  publicKey " + BINARY_TYPE + " NOT NULL," +
                "  handshakePublicKey " + BINARY_TYPE + "," +
                "  localAuthorId " + HASH_TYPE + " NOT NULL," +
                "  verified BOOLEAN NOT NULL," +
                "  syncVersions " + BINARY_TYPE + " DEFAULT X'00' NOT NULL," +
                "  PRIMARY KEY (contactId)," +
                "  FOREIGN KEY (localAuthorId) REFERENCES localAuthors (authorId) ON DELETE CASCADE" +
                ")";

        private static final String CREATE_GROUPS =
                "CREATE TABLE groups (" +
                "  groupId " + HASH_TYPE + " NOT NULL," +
                "  clientId " + STRING_TYPE + " NOT NULL," +
                "  majorVersion INT NOT NULL," +
                "  descriptor " + BINARY_TYPE + " NOT NULL," +
                "  PRIMARY KEY (groupId)" +
                ")";

        private static final String CREATE_MESSAGES =
                "CREATE TABLE messages (" +
                "  messageId " + HASH_TYPE + " NOT NULL," +
                "  groupId " + HASH_TYPE + " NOT NULL," +
                "  timestamp BIGINT NOT NULL," +
                "  state INT NOT NULL," +
                "  shared BOOLEAN NOT NULL," +
                "  temporary BOOLEAN NOT NULL," +
                "  cleanupTimerDuration BIGINT," +
                "  cleanupDeadline BIGINT," +
                "  length INT NOT NULL," +
                "  raw " + BINARY_TYPE + "," +
                "  PRIMARY KEY (messageId)," +
                "  FOREIGN KEY (groupId) REFERENCES groups (groupId) ON DELETE CASCADE" +
                ")";

        private static final String CREATE_MESSAGE_METADATA =
                "CREATE TABLE messageMetadata (" +
                "  messageId " + HASH_TYPE + " NOT NULL," +
                "  groupId " + HASH_TYPE + " NOT NULL," +
                "  metaKey " + STRING_TYPE + " NOT NULL," +
                "  value " + BINARY_TYPE + " NOT NULL," +
                "  PRIMARY KEY (messageId, metaKey)," +
                "  FOREIGN KEY (messageId) REFERENCES messages (messageId) ON DELETE CASCADE" +
                ")";

        private static final String CREATE_STATUSES =
                "CREATE TABLE statuses (" +
                "  messageId " + HASH_TYPE + " NOT NULL," +
                "  contactId INT NOT NULL," +
                "  groupId " + HASH_TYPE + " NOT NULL," +
                "  timestamp BIGINT NOT NULL," +
                "  length INT NOT NULL," +
                "  state INT NOT NULL," +
                "  groupShared BOOLEAN NOT NULL," +
                "  messageShared BOOLEAN NOT NULL," +
                "  deleted BOOLEAN NOT NULL," +
                "  ack BOOLEAN NOT NULL," +
                "  seen BOOLEAN NOT NULL," +
                "  requested BOOLEAN NOT NULL," +
                "  expiry BIGINT NOT NULL," +
                "  txCount INT NOT NULL," +
                "  maxLatency BIGINT," +
                "  PRIMARY KEY (messageId, contactId)," +
                "  FOREIGN KEY (messageId) REFERENCES messages (messageId) ON DELETE CASCADE," +
                "  FOREIGN KEY (contactId) REFERENCES contacts (contactId) ON DELETE CASCADE" +
                ")";

        public void createTables(Connection conn) throws SQLException {
            try (Statement stmt = conn.createStatement()) {
                // 按依赖顺序创建表
                stmt.executeUpdate(CREATE_SETTINGS);
                stmt.executeUpdate(CREATE_LOCAL_AUTHORS);
                stmt.executeUpdate(CREATE_CONTACTS);
                stmt.executeUpdate(CREATE_GROUPS);
                stmt.executeUpdate(CREATE_MESSAGES);
                stmt.executeUpdate(CREATE_MESSAGE_METADATA);
                stmt.executeUpdate(CREATE_STATUSES);

                // 创建索引
                createIndexes(stmt);

                conn.commit();
            }
        }

        private void createIndexes(Statement stmt) throws SQLException {
            // 消息表索引
            stmt.executeUpdate("CREATE INDEX idx_messages_group ON messages (groupId)");
            stmt.executeUpdate("CREATE INDEX idx_messages_timestamp ON messages (timestamp)");

            // 状态表索引
            stmt.executeUpdate("CREATE INDEX idx_statuses_contact ON statuses (contactId)");
            stmt.executeUpdate("CREATE INDEX idx_statuses_group ON statuses (groupId)");
            stmt.executeUpdate("CREATE INDEX idx_statuses_timestamp ON statuses (timestamp)");

            // 联系人表索引
            stmt.executeUpdate("CREATE INDEX idx_contacts_author ON contacts (authorId)");
            stmt.executeUpdate("CREATE INDEX idx_contacts_local_author ON contacts (localAuthorId)");

            // 消息元数据索引
            stmt.executeUpdate("CREATE INDEX idx_message_metadata_group ON messageMetadata (groupId)");
        }

        public boolean tableExists(Connection conn, String tableName) throws SQLException {
            DatabaseMetaData metaData = conn.getMetaData();
            try (ResultSet tables = metaData.getTables(null, null, tableName.toUpperCase(), null)) {
                return tables.next();
            }
        }

        public void dropAllTables(Connection conn) throws SQLException {
            try (Statement stmt = conn.createStatement()) {
                // 按相反的依赖顺序删除表
                String[] tables = {
                    "statuses", "messageMetadata", "messages",
                    "groups", "contacts", "localAuthors", "settings"
                };

                for (String table : tables) {
                    try {
                        stmt.executeUpdate("DROP TABLE IF EXISTS " + table);
                    } catch (SQLException e) {
                        // 忽略表不存在的错误
                        System.err.println("删除表 " + table + " 时发生异常: " + e.getMessage());
                    }
                }

                conn.commit();
            }
        }
    }
}
