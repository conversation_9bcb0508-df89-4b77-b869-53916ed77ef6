# Briar核心代码片段收集

## 🔐 加密和安全机制深度解析

### 核心加密算法

#### XSalsa20Poly1305 认证加密
```java
/**
 * 使用XSalsa20进行加密，Poly1305进行认证的认证加密算法
 * 等价于NaCl的crypto_secretbox实现
 */
class XSalsa20Poly1305AuthenticatedCipher implements AuthenticatedCipher {
    private static final int SUBKEY_LENGTH = 32;
    private final XSalsa20Engine xSalsa20Engine;
    private final Poly1305 poly1305;

    @Override
    public void init(boolean encrypt, SecretKey key, byte[] iv)
            throws GeneralSecurityException {
        encrypting = encrypt;
        KeyParameter k = new KeyParameter(key.getBytes());
        ParametersWithIV params = new ParametersWithIV(k, iv);
        xSalsa20Engine.init(encrypt, params);
    }

    @Override
    public int process(byte[] input, int inputOff, int len, byte[] output,
            int outputOff) throws GeneralSecurityException {
        // 生成Poly1305子密钥
        byte[] zero = new byte[SUBKEY_LENGTH];
        byte[] subKey = new byte[SUBKEY_LENGTH];
        xSalsa20Engine.processBytes(zero, 0, SUBKEY_LENGTH, subKey, 0);

        // 钳制子密钥
        Poly1305KeyGenerator.clamp(subKey);

        // 初始化Poly1305
        KeyParameter k = new KeyParameter(subKey);
        poly1305.init(k);

        if (!encrypting) {
            // 解密时验证MAC
            byte[] mac = new byte[MAC_LENGTH];
            poly1305.update(input, inputOff + MAC_LENGTH, len - MAC_LENGTH);
            poly1305.doFinal(mac, 0);
            // 常量时间比较
            int cmp = 0;
            for (int i = 0; i < MAC_LENGTH; i++)
                cmp |= mac[i] ^ input[inputOff + i];
            if (cmp != 0) throw new GeneralSecurityException("Invalid MAC");
        }

        // 应用流加密
        int processed = xSalsa20Engine.processBytes(
            input, encrypting ? inputOff : inputOff + MAC_LENGTH,
            encrypting ? len : len - MAC_LENGTH,
            output, encrypting ? outputOff + MAC_LENGTH : outputOff);

        if (encrypting) {
            // 加密时生成MAC
            poly1305.update(output, outputOff + MAC_LENGTH, len);
            poly1305.doFinal(output, outputOff);
        }

        return processed + (encrypting ? MAC_LENGTH : 0);
    }
}
```

#### Ed25519 数字签名
```java
/**
 * Ed25519椭圆曲线数字签名算法实现
 */
class EdSignature implements Signature {
    private static final Provider PROVIDER = new EdDSASecurityProvider();
    private static final EdDSANamedCurveSpec CURVE_SPEC =
            EdDSANamedCurveTable.getByName("Ed25519");

    private final java.security.Signature signature;

    @Override
    public void initSign(PrivateKey k) throws GeneralSecurityException {
        if (!k.getKeyType().equals(KEY_TYPE_SIGNATURE))
            throw new IllegalArgumentException();
        EdDSAPrivateKey privateKey = new EdDSAPrivateKey(
                new EdDSAPrivateKeySpec(k.getEncoded(), CURVE_SPEC));
        signature.initSign(privateKey);
    }

    @Override
    public void update(byte[] data) throws GeneralSecurityException {
        signature.update(data);
    }

    @Override
    public byte[] sign() throws GeneralSecurityException {
        return signature.sign();
    }

    @Override
    public boolean verify(byte[] sig, PublicKey k) throws GeneralSecurityException {
        if (!k.getKeyType().equals(KEY_TYPE_SIGNATURE))
            throw new IllegalArgumentException();
        EdDSAPublicKey publicKey = new EdDSAPublicKey(
                new EdDSAPublicKeySpec(k.getEncoded(), CURVE_SPEC));
        signature.initVerify(publicKey);
        return signature.verify(sig);
    }
}
```

#### Scrypt 密钥派生函数
```java
/**
 * Scrypt密钥派生函数，用于从密码派生加密密钥
 */
class ScryptKdf implements PasswordBasedKdf {
    private static final int MIN_COST = 256;      // 最小参数N
    private static final int MAX_COST = 1024 * 1024; // 最大参数N
    private static final int BLOCK_SIZE = 8;      // 参数r
    private static final int PARALLELIZATION = 1; // 参数p
    private static final int TARGET_MS = 1000;    // 目标时间1秒

    @Override
    public int chooseCostParameter() {
        // Scrypt使用至少128 * N * r字节内存
        // 不要使用超过JVM最大堆大小的一半
        long maxMemory = Runtime.getRuntime().maxMemory();
        long maxCost = min(MAX_COST, maxMemory / BLOCK_SIZE / 256);

        // 从最小值增加到最大值，同时测量性能
        int cost = MIN_COST;
        while (cost * 2 <= maxCost && measureDuration(cost) * 2 <= TARGET_MS) {
            cost *= 2;
        }
        return cost;
    }

    @Override
    public SecretKey deriveKey(String password, byte[] salt, int cost) {
        byte[] passwordBytes = StringUtils.toUtf8(password);
        SecretKey k = new SecretKey(SCrypt.generate(passwordBytes, salt, cost,
                BLOCK_SIZE, PARALLELIZATION, SecretKey.LENGTH));
        return k;
    }
}
```

### 密钥管理和协商

#### 密钥派生机制
```java
@Override
public SecretKey deriveKey(String label, SecretKey k, byte[]... inputs) {
    byte[] mac = mac(label, k, inputs);
    if (mac.length != SecretKey.LENGTH) throw new IllegalStateException();
    return new SecretKey(mac);
}

@Override
public SecretKey deriveSharedSecret(String label, PublicKey theirPublicKey,
        KeyPair ourKeyPair, byte[]... inputs) throws GeneralSecurityException {
    PrivateKey ourPrivateKey = ourKeyPair.getPrivate();
    byte[][] hashInputs = new byte[inputs.length + 1][];
    // 执行原始密钥协商（Curve25519）
    hashInputs[0] = performRawKeyAgreement(ourPrivateKey, theirPublicKey);
    arraycopy(inputs, 0, hashInputs, 1, inputs.length);
    byte[] hash = hash(label, hashInputs);
    if (hash.length != SecretKey.LENGTH) throw new IllegalStateException();
    return new SecretKey(hash);
}
```

#### BQP密钥协商协议
```java
/**
 * BQP (Bramble QR Protocol) 密钥协商协议实现
 */
class KeyAgreementProtocol {
    SecretKey perform() throws AbortException, IOException {
        try {
            PublicKey theirPublicKey;
            if (alice) {
                sendKey();
                // Alice等待Bob扫描她的二维码
                callbacks.connectionWaiting();
                theirPublicKey = receiveKey();
            } else {
                theirPublicKey = receiveKey();
                sendKey();
            }

            SecretKey s = deriveSharedSecret(theirPublicKey);
            if (alice) {
                sendConfirm(s, theirPublicKey);
                receiveConfirm(s, theirPublicKey);
            } else {
                receiveConfirm(s, theirPublicKey);
                sendConfirm(s, theirPublicKey);
            }
            return crypto.deriveKey(MASTER_KEY_LABEL, s);
        } catch (AbortException e) {
            sendAbort(e.getCause() != null);
            throw e;
        }
    }

    private PublicKey receiveKey() throws AbortException {
        byte[] publicKeyBytes = transport.receiveKey();
        callbacks.initialRecordReceived();
        KeyParser keyParser = crypto.getAgreementKeyParser();
        try {
            PublicKey publicKey = keyParser.parsePublicKey(publicKeyBytes);
            // 验证密钥承诺
            byte[] expected = keyAgreementCrypto.deriveKeyCommitment(publicKey);
            if (!Arrays.equals(expected, theirPayload.getCommitment()))
                throw new AbortException();
            return publicKey;
        } catch (GeneralSecurityException e) {
            throw new AbortException();
        }
    }
}
```

### 消息认证码 (MAC)

#### Blake2b MAC实现
```java
@Override
public byte[] mac(String label, SecretKey macKey, byte[]... inputs) {
    byte[] labelBytes = StringUtils.toUtf8(label);
    // 使用Blake2b作为MAC函数，密钥长度32字节
    Digest mac = new Blake2bDigest(macKey.getBytes(), 32, null, null);
    byte[] length = new byte[INT_32_BYTES];

    // 添加标签长度和标签
    ByteUtils.writeUint32(labelBytes.length, length, 0);
    mac.update(length, 0, length.length);
    mac.update(labelBytes, 0, labelBytes.length);

    // 添加每个输入的长度和内容
    for (byte[] input : inputs) {
        ByteUtils.writeUint32(input.length, length, 0);
        mac.update(length, 0, length.length);
        mac.update(input, 0, input.length);
    }

    byte[] output = new byte[mac.getDigestSize()];
    mac.doFinal(output, 0);
    return output;
}

@Override
public boolean verifyMac(byte[] mac, String label, SecretKey macKey,
        byte[]... inputs) {
    byte[] expected = mac(label, macKey, inputs);
    if (mac.length != expected.length) return false;
    // 常量时间比较防止时序攻击
    int cmp = 0;
    for (int i = 0; i < mac.length; i++) cmp |= mac[i] ^ expected[i];
    return cmp == 0;
}
```

### 密码加密和密钥强化

#### 基于密码的加密
```java
@Override
public byte[] encryptWithPassword(byte[] input, String password,
        @Nullable KeyStrengthener keyStrengthener) {
    AuthenticatedCipher cipher = new XSalsa20Poly1305AuthenticatedCipher();
    int macBytes = cipher.getMacBytes();

    // 生成随机盐
    byte[] salt = new byte[PBKDF_SALT_BYTES];
    secureRandom.nextBytes(salt);

    // 校准KDF成本参数
    int cost = passwordBasedKdf.chooseCostParameter();

    // 从密码派生加密密钥
    SecretKey key = passwordBasedKdf.deriveKey(password, salt, cost);
    if (keyStrengthener != null) key = keyStrengthener.strengthenKey(key);

    // 生成随机IV
    byte[] iv = new byte[STORAGE_IV_BYTES];
    secureRandom.nextBytes(iv);

    // 输出包含：格式版本、盐、成本参数、IV、密文和MAC
    int outputLen = 1 + salt.length + INT_32_BYTES + iv.length
            + input.length + macBytes;
    byte[] output = new byte[outputLen];
    int outputOff = 0;

    // 格式版本
    byte formatVersion = keyStrengthener == null
            ? PBKDF_FORMAT_SCRYPT : PBKDF_FORMAT_SCRYPT_STRENGTHENED;
    output[outputOff] = formatVersion;
    outputOff++;

    // 盐
    arraycopy(salt, 0, output, outputOff, salt.length);
    outputOff += salt.length;

    // 成本参数
    ByteUtils.writeUint32(cost, output, outputOff);
    outputOff += INT_32_BYTES;

    // IV
    arraycopy(iv, 0, output, outputOff, iv.length);
    outputOff += iv.length;

    // 初始化密码器并加密明文
    try {
        cipher.init(true, key, iv);
        cipher.process(input, 0, input.length, output, outputOff);
        return output;
    } catch (GeneralSecurityException e) {
        throw new RuntimeException(e);
    }
}
```

### 安全随机数生成

#### Unix平台安全随机数
```java
/**
 * Unix平台的安全随机数提供者，使用/dev/urandom
 */
class UnixSecureRandomProvider extends AbstractSecureRandomProvider {
    private static final File RANDOM_DEVICE = new File("/dev/urandom");
    private final AtomicBoolean seeded = new AtomicBoolean(false);

    @Override
    public Provider getProvider() {
        if (!seeded.getAndSet(true)) writeSeed();
        return new UnixProvider();
    }

    private static class UnixProvider extends Provider {
        private UnixProvider() {
            super("UnixPRNG", 1.0, "A Unix-specific PRNG using /dev/urandom");
            // 虽然/dev/urandom不是SHA-1 PRNG，但一些调用者
            // 明确请求SHA1PRNG SecureRandom，我们需要防止他们
            // 获得默认实现，其输出可能具有低熵
            put("SecureRandom.SHA1PRNG", UnixSecureRandomSpi.class.getName());
            put("SecureRandom.SHA1PRNG ImplementedIn", "Software");
        }
    }
}
```

#### Android平台安全随机数增强
```java
/**
 * Android平台的安全随机数提供者，增加额外的熵源
 */
class AndroidSecureRandomProvider extends AbstractSecureRandomProvider {
    @Override
    protected void writeToEntropyPool(DataOutputStream out) throws IOException {
        super.writeToEntropyPool(out);
        // 添加进程相关信息
        out.writeInt(myPid());
        out.writeInt(myTid());
        out.writeInt(myUid());

        // 添加设备指纹信息
        if (FINGERPRINT != null) out.writeUTF(FINGERPRINT);
        if (SERIAL != null) out.writeUTF(SERIAL);

        // 添加Android ID
        ContentResolver contentResolver = appContext.getContentResolver();
        String id = Settings.Secure.getString(contentResolver, ANDROID_ID);
        if (id != null) out.writeUTF(id);

        // 在API 31以下添加蓝牙设备信息
        if (SDK_INT < 31) {
            BluetoothAdapter bt = BluetoothAdapter.getDefaultAdapter();
            if (bt != null) {
                // 添加已配对设备信息作为额外熵
                // ...
            }
        }
    }
}
```

### 身份管理和验证

#### 身份创建和管理
```java
/**
 * 身份管理器实现
 */
class IdentityManagerImpl implements IdentityManager {
    @Override
    public Identity createIdentity(String name) {
        long start = now();
        // 创建本地作者
        LocalAuthor localAuthor = authorFactory.createLocalAuthor(name);
        // 生成握手密钥对（Curve25519）
        KeyPair handshakeKeyPair = crypto.generateAgreementKeyPair();
        PublicKey handshakePub = handshakeKeyPair.getPublic();
        PrivateKey handshakePriv = handshakeKeyPair.getPrivate();
        logDuration(LOG, "Creating identity", start);
        return new Identity(localAuthor, handshakePub, handshakePriv,
                clock.currentTimeMillis());
    }

    @Override
    public void registerIdentity(Identity i) {
        if (!i.hasHandshakeKeyPair()) throw new IllegalArgumentException();
        cachedIdentity = i;
        shouldStoreIdentity = true;
        LOG.info("Identity registered");
    }

    @Override
    public void onDatabaseOpened(Transaction txn) throws DbException {
        Identity cached = getCachedIdentity(txn);
        if (shouldStoreIdentity) {
            // 身份在启动时注册 - 存储它
            db.addIdentity(txn, cached);
            LOG.info("Identity stored");
        } else if (shouldStoreKeys) {
            // 加载身份时生成了握手密钥 - 存储它们
            PublicKey handshakePub = requireNonNull(cached.getHandshakePublicKey());
            PrivateKey handshakePriv = requireNonNull(cached.getHandshakePrivateKey());
            db.setHandshakeKeyPair(txn, cached.getId(), handshakePub, handshakePriv);
            LOG.info("Handshake key pair stored");
        }
    }
}
```

#### 联系人交换加密
```java
/**
 * 联系人交换过程中的加密操作
 */
class ContactExchangeCryptoImpl implements ContactExchangeCrypto {
    @Override
    public byte[] sign(PrivateKey privateKey, SecretKey masterKey, boolean alice) {
        byte[] nonce = deriveNonce(masterKey, alice);
        try {
            return crypto.sign(SIGNING_LABEL, nonce, privateKey);
        } catch (GeneralSecurityException e) {
            throw new AssertionError();
        }
    }

    @Override
    public boolean verify(PublicKey publicKey, SecretKey masterKey,
            boolean alice, byte[] signature) {
        byte[] nonce = deriveNonce(masterKey, alice);
        try {
            return crypto.verifySignature(signature, SIGNING_LABEL, nonce, publicKey);
        } catch (GeneralSecurityException e) {
            return false;
        }
    }

    private byte[] deriveNonce(SecretKey masterKey, boolean alice) {
        String label = alice ? ALICE_NONCE_LABEL : BOB_NONCE_LABEL;
        return crypto.mac(label, masterKey, PROTOCOL_VERSION_BYTES);
    }
}
```

## 🏗️ 依赖注入架构

### BrambleCoreModule - 核心模块定义
```java
@Module(includes = {
    CleanupModule.class,
    ClientModule.class,
    ConnectionModule.class,
    ContactModule.class,
    CryptoModule.class,
    CryptoExecutorModule.class,
    DataModule.class,
    DatabaseModule.class,
    DatabaseExecutorModule.class,
    EventModule.class,
    IdentityModule.class,
    IoModule.class,
    KeyAgreementModule.class,
    LifecycleModule.class,
    MailboxModule.class,
    PluginModule.class,
    PropertiesModule.class,
    QrCodeModule.class,
    RecordModule.class,
    ReliabilityModule.class,
    RendezvousModule.class,
    SettingsModule.class,
    SyncModule.class,
    TransportKeyAgreementModule.class,
    TransportModule.class,
    ValidationModule.class,
    VersioningModule.class
})
public class BrambleCoreModule {
}
```

### DatabaseModule - 数据库模块配置
```java
@Module
public class DatabaseModule {
    @Provides
    @Singleton
    Database<Connection> provideDatabase(DatabaseConfig config,
            MessageFactory messageFactory, Clock clock) {
        return new H2Database(config, messageFactory, clock);
    }

    @Provides
    @Singleton
    DatabaseComponent provideDatabaseComponent(Database<Connection> db,
            EventBus eventBus, @EventExecutor Executor eventExecutor,
            ShutdownManager shutdownManager) {
        return new DatabaseComponentImpl<>(db, Connection.class, eventBus,
                eventExecutor, shutdownManager);
    }

    @Provides
    TransactionManager provideTransactionManager(DatabaseComponent db) {
        return db;
    }
}
```

## 🗄️ 数据库层实现

### 数据库表创建 - Messages表
```java
private static final String CREATE_MESSAGES =
    "CREATE TABLE messages"
        + " (messageId _HASH NOT NULL,"
        + " groupId _HASH NOT NULL,"
        + " timestamp BIGINT NOT NULL,"
        + " state INT NOT NULL,"
        + " shared BOOLEAN NOT NULL,"
        + " temporary BOOLEAN NOT NULL,"
        // 自动删除相关字段
        + " cleanupTimerDuration BIGINT," // Null if no timer duration has been set
        + " cleanupDeadline BIGINT,"      // Null if no timer duration has been set or the timer hasn't started
        + " length INT NOT NULL,"
        + " raw BLOB," // Null if message has been deleted
        + " PRIMARY KEY (messageId),"
        + " FOREIGN KEY (groupId)"
        + " REFERENCES groups (groupId)"
        + " ON DELETE CASCADE)";
```

### 索引创建策略
```java
// 核心索引定义
private static final String INDEX_CONTACTS_BY_AUTHOR_ID =
    "CREATE INDEX IF NOT EXISTS contactsByAuthorId"
    + " ON contacts (authorId)";

private static final String INDEX_STATUSES_BY_CONTACT_ID_TIMESTAMP =
    "CREATE INDEX IF NOT EXISTS statusesByContactIdTimestamp"
    + " ON statuses (contactId, timestamp)";

private static final String INDEX_MESSAGES_BY_CLEANUP_DEADLINE =
    "CREATE INDEX IF NOT EXISTS messagesByCleanupDeadline"
    + " ON messages (cleanupDeadline)";

// 索引创建方法
private void createIndexes(Connection txn) throws DbException {
    Statement s = null;
    try {
        s = txn.createStatement();
        s.executeUpdate(INDEX_CONTACTS_BY_AUTHOR_ID);
        s.executeUpdate(INDEX_GROUPS_BY_CLIENT_ID_MAJOR_VERSION);
        s.executeUpdate(INDEX_MESSAGE_METADATA_BY_GROUP_ID_STATE);
        s.executeUpdate(INDEX_MESSAGE_DEPENDENCIES_BY_DEPENDENCY_ID);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_GROUP_ID);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_TIMESTAMP);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_TX_COUNT_TIMESTAMP);
        s.executeUpdate(INDEX_MESSAGES_BY_CLEANUP_DEADLINE);
        s.close();
    } catch (SQLException e) {
        tryToClose(s, LOG, WARNING);
        throw new DbException(e);
    }
}
```

### 事务管理 - 读写锁分离
```java
@Override
public Transaction startTransaction(boolean readOnly) throws DbException {
    // 防止重入锁
    if (lock.getReadHoldCount() > 0) throw new IllegalStateException();
    if (lock.getWriteHoldCount() > 0) throw new IllegalStateException();
    
    long start = now();
    if (readOnly) {
        lock.readLock().lock();
        logDuration(LOG, "Waiting for read lock", start);
    } else {
        lock.writeLock().lock();
        logDuration(LOG, "Waiting for write lock", start);
    }
    try {
        return new Transaction(db.startTransaction(), readOnly);
    } catch (DbException | RuntimeException e) {
        if (readOnly) lock.readLock().unlock();
        else lock.writeLock().unlock();
        throw e;
    }
}
```

### 数据库迁移机制
```java
// 应用数据库迁移
for (Migration<Connection> m : getMigrations()) {
    int start = m.getStartVersion(), end = m.getEndVersion();
    if (start == dataSchemaVersion) {
        if (LOG.isLoggable(INFO))
            LOG.info("Migrating from schema " + start + " to " + end);
        if (listener != null) listener.onDatabaseMigration();
        // 应用迁移
        m.migrate(txn);
        // 存储新的schema版本
        storeSchemaVersion(txn, end);
        dataSchemaVersion = end;
    }
}

// 迁移列表
List<Migration<Connection>> getMigrations() {
    return asList(
        new Migration38_39(),
        new Migration39_40(),
        new Migration40_41(dbTypes),
        new Migration41_42(dbTypes),
        new Migration42_43(dbTypes),
        new Migration43_44(dbTypes),
        new Migration44_45(),
        new Migration45_46(),
        new Migration46_47(dbTypes),
        new Migration47_48(),
        new Migration48_49(),
        new Migration49_50()
    );
}
```

## 🧹 自动清理机制

### CleanupManager - 清理管理器
```java
@Override
public void eventOccurred(Event e) {
    if (e instanceof CleanupTimerStartedEvent) {
        CleanupTimerStartedEvent a = (CleanupTimerStartedEvent) e;
        maybeScheduleTask(a.getCleanupDeadline());
    }
}

private void maybeScheduleTask(long deadline) {
    synchronized (lock) {
        for (CleanupTask task : pending) {
            if (task.deadline <= deadline) return;
        }
        CleanupTask task = new CleanupTask(deadline);
        pending.add(task);
        scheduleTask(task);
    }
}

private void scheduleTask(CleanupTask task) {
    long now = clock.currentTimeMillis();
    long delay = max(0, task.deadline - now + BATCH_DELAY_MS);
    if (LOG.isLoggable(INFO)) {
        LOG.info("Scheduling cleanup task in " + delay + " ms");
    }
    taskScheduler.schedule(() -> deleteMessagesAndScheduleNextTask(task),
            dbExecutor, delay, MILLISECONDS);
}
```

### 清理任务执行
```java
private void deleteMessagesAndScheduleNextTask(CleanupTask task) {
    try {
        synchronized (lock) {
            pending.remove(task);
        }
        long deadline = db.transactionWithResult(false, txn -> {
            deleteMessages(txn);
            return db.getNextCleanupDeadline(txn);
        });
        if (deadline != NO_CLEANUP_DEADLINE) {
            maybeScheduleTask(deadline);
        }
    } catch (DbException e) {
        logException(LOG, WARNING, e);
    }
}

private void deleteMessages(Transaction txn) throws DbException {
    Map<GroupId, Collection<MessageId>> ids = db.getMessagesToDelete(txn);
    for (Entry<GroupId, Collection<MessageId>> e : ids.entrySet()) {
        GroupId groupId = e.getKey();
        Collection<MessageId> messageIds = e.getValue();
        if (LOG.isLoggable(INFO)) {
            LOG.info(messageIds.size() + " messages to delete");
        }
        for (MessageId m : messageIds) db.stopCleanupTimer(txn, m);
        Group group = db.getGroup(txn, groupId);
        ClientMajorVersion cv = new ClientMajorVersion(group.getClientId(),
                group.getMajorVersion());
        CleanupHook hook = hooks.get(cv);
        if (hook == null) {
            throw new IllegalStateException("No cleanup hook for " + cv);
        }
        hook.deleteMessages(txn, groupId, messageIds);
    }
}
```

### 清理定时器操作
```java
@Override
public long startCleanupTimer(Connection txn, MessageId m) throws DbException {
    long now = clock.currentTimeMillis();
    PreparedStatement ps = null;
    ResultSet rs = null;
    try {
        String sql = "UPDATE messages"
                + " SET cleanupDeadline = ? + cleanupTimerDuration"
                + " WHERE messageId = ?"
                + " AND cleanupTimerDuration IS NOT NULL"
                + " AND cleanupDeadline IS NULL";
        ps = txn.prepareStatement(sql);
        ps.setLong(1, now);
        ps.setBytes(2, m.getBytes());
        int affected = ps.executeUpdate();
        if (affected < 0 || affected > 1) throw new DbStateException();
        ps.close();
        if (affected == 0) return TIMER_NOT_STARTED;
        
        sql = "SELECT cleanupDeadline FROM messages WHERE messageId = ?";
        ps = txn.prepareStatement(sql);
        ps.setBytes(1, m.getBytes());
        rs = ps.executeQuery();
        if (!rs.next()) throw new DbStateException();
        long deadline = rs.getLong(1);
        if (rs.next()) throw new DbStateException();
        rs.close();
        ps.close();
        return deadline;
    } catch (SQLException e) {
        tryToClose(rs, LOG, WARNING);
        tryToClose(ps, LOG, WARNING);
        throw new DbException(e);
    }
}
```

## 📡 事件系统

### EventBus - 事件总线实现
```java
@ThreadSafe
@NotNullByDefault
class EventBusImpl implements EventBus {
    private final Collection<EventListener> listeners =
            new CopyOnWriteArrayList<>();
    private final Executor eventExecutor;

    @Inject
    EventBusImpl(@EventExecutor Executor eventExecutor) {
        this.eventExecutor = eventExecutor;
    }

    @Override
    public void addListener(EventListener l) {
        listeners.add(l);
    }

    @Override
    public void removeListener(EventListener l) {
        listeners.remove(l);
    }

    @Override
    public void broadcast(Event e) {
        eventExecutor.execute(() -> {
            for (EventListener l : listeners) l.eventOccurred(e);
        });
    }
}
```

### 事件监听器示例
```java
@Override
public void eventOccurred(Event e) {
    if (e instanceof SettingsUpdatedEvent) {
        SettingsUpdatedEvent s = (SettingsUpdatedEvent) e;
        if (s.getNamespace().equals(ID.getString()))
            ioExecutor.execute(() -> onSettingsUpdated(s.getSettings()));
    } else if (e instanceof KeyAgreementListeningEvent) {
        connectionLimiter.startLimiting();
    } else if (e instanceof KeyAgreementStoppedListeningEvent) {
        connectionLimiter.endLimiting();
    } else if (e instanceof RemoteTransportPropertiesUpdatedEvent) {
        RemoteTransportPropertiesUpdatedEvent r =
                (RemoteTransportPropertiesUpdatedEvent) e;
        if (r.getTransportId().equals(ID)) {
            ioExecutor.execute(this::updateProperties);
        }
    }
}
```

## 🚀 生命周期管理

### LifecycleManager - 服务启动
```java
@Override
public StartResult startServices(SecretKey dbKey) {
    if (!state.compareAndSet(CREATED, STARTING)) {
        LOG.warning("Already running");
        return ALREADY_RUNNING;
    }
    
    long now = clock.currentTimeMillis();
    if (now < MIN_REASONABLE_TIME_MS || now > MAX_REASONABLE_TIME_MS) {
        if (LOG.isLoggable(WARNING)) {
            LOG.warning("System clock is unreasonable: " + now);
        }
        return CLOCK_ERROR;
    }

    try {
        LOG.info("Opening database");
        state.set(STARTING);
        eventBus.broadcast(new LifecycleEvent(STARTING));
        
        boolean reopened = db.open(dbKey, this);
        
        LOG.info("Starting services");
        state.set(STARTING_SERVICES);
        dbLatch.countDown();
        eventBus.broadcast(new LifecycleEvent(STARTING_SERVICES));

        for (Service s : services) {
            start = now();
            s.startService();
            if (LOG.isLoggable(FINE)) {
                logDuration(LOG, "Starting service "
                        + s.getClass().getSimpleName(), start);
            }
        }

        state.set(RUNNING);
        startupLatch.countDown();
        eventBus.broadcast(new LifecycleEvent(RUNNING));
        return SUCCESS;
    } catch (DataTooOldException e) {
        logException(LOG, WARNING, e);
        return DATA_TOO_OLD_ERROR;
    } catch (DataTooNewException e) {
        logException(LOG, WARNING, e);
        return DATA_TOO_NEW_ERROR;
    } catch (DbException e) {
        logException(LOG, WARNING, e);
        return DB_ERROR;
    } catch (ServiceException e) {
        logException(LOG, WARNING, e);
        return SERVICE_ERROR;
    }
}
```

---

这些代码片段展示了Briar项目的核心实现细节，包括依赖注入、数据库操作、事件处理、自动清理和生命周期管理等关键功能的具体实现方法。
