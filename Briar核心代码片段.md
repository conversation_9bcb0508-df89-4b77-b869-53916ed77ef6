# <PERSON>riar核心代码片段收集

## 🏗️ 依赖注入架构

### BrambleCoreModule - 核心模块定义
```java
@Module(includes = {
    CleanupModule.class,
    ClientModule.class,
    ConnectionModule.class,
    ContactModule.class,
    CryptoModule.class,
    CryptoExecutorModule.class,
    DataModule.class,
    DatabaseModule.class,
    DatabaseExecutorModule.class,
    EventModule.class,
    IdentityModule.class,
    IoModule.class,
    KeyAgreementModule.class,
    LifecycleModule.class,
    MailboxModule.class,
    PluginModule.class,
    PropertiesModule.class,
    QrCodeModule.class,
    RecordModule.class,
    ReliabilityModule.class,
    RendezvousModule.class,
    SettingsModule.class,
    SyncModule.class,
    TransportKeyAgreementModule.class,
    TransportModule.class,
    ValidationModule.class,
    VersioningModule.class
})
public class BrambleCoreModule {
}
```

### DatabaseModule - 数据库模块配置
```java
@Module
public class DatabaseModule {
    @Provides
    @Singleton
    Database<Connection> provideDatabase(DatabaseConfig config,
            MessageFactory messageFactory, Clock clock) {
        return new H2Database(config, messageFactory, clock);
    }

    @Provides
    @Singleton
    DatabaseComponent provideDatabaseComponent(Database<Connection> db,
            EventBus eventBus, @EventExecutor Executor eventExecutor,
            ShutdownManager shutdownManager) {
        return new DatabaseComponentImpl<>(db, Connection.class, eventBus,
                eventExecutor, shutdownManager);
    }

    @Provides
    TransactionManager provideTransactionManager(DatabaseComponent db) {
        return db;
    }
}
```

## 🗄️ 数据库层实现

### 数据库表创建 - Messages表
```java
private static final String CREATE_MESSAGES =
    "CREATE TABLE messages"
        + " (messageId _HASH NOT NULL,"
        + " groupId _HASH NOT NULL,"
        + " timestamp BIGINT NOT NULL,"
        + " state INT NOT NULL,"
        + " shared BOOLEAN NOT NULL,"
        + " temporary BOOLEAN NOT NULL,"
        // 自动删除相关字段
        + " cleanupTimerDuration BIGINT," // Null if no timer duration has been set
        + " cleanupDeadline BIGINT,"      // Null if no timer duration has been set or the timer hasn't started
        + " length INT NOT NULL,"
        + " raw BLOB," // Null if message has been deleted
        + " PRIMARY KEY (messageId),"
        + " FOREIGN KEY (groupId)"
        + " REFERENCES groups (groupId)"
        + " ON DELETE CASCADE)";
```

### 索引创建策略
```java
// 核心索引定义
private static final String INDEX_CONTACTS_BY_AUTHOR_ID =
    "CREATE INDEX IF NOT EXISTS contactsByAuthorId"
    + " ON contacts (authorId)";

private static final String INDEX_STATUSES_BY_CONTACT_ID_TIMESTAMP =
    "CREATE INDEX IF NOT EXISTS statusesByContactIdTimestamp"
    + " ON statuses (contactId, timestamp)";

private static final String INDEX_MESSAGES_BY_CLEANUP_DEADLINE =
    "CREATE INDEX IF NOT EXISTS messagesByCleanupDeadline"
    + " ON messages (cleanupDeadline)";

// 索引创建方法
private void createIndexes(Connection txn) throws DbException {
    Statement s = null;
    try {
        s = txn.createStatement();
        s.executeUpdate(INDEX_CONTACTS_BY_AUTHOR_ID);
        s.executeUpdate(INDEX_GROUPS_BY_CLIENT_ID_MAJOR_VERSION);
        s.executeUpdate(INDEX_MESSAGE_METADATA_BY_GROUP_ID_STATE);
        s.executeUpdate(INDEX_MESSAGE_DEPENDENCIES_BY_DEPENDENCY_ID);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_GROUP_ID);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_TIMESTAMP);
        s.executeUpdate(INDEX_STATUSES_BY_CONTACT_ID_TX_COUNT_TIMESTAMP);
        s.executeUpdate(INDEX_MESSAGES_BY_CLEANUP_DEADLINE);
        s.close();
    } catch (SQLException e) {
        tryToClose(s, LOG, WARNING);
        throw new DbException(e);
    }
}
```

### 事务管理 - 读写锁分离
```java
@Override
public Transaction startTransaction(boolean readOnly) throws DbException {
    // 防止重入锁
    if (lock.getReadHoldCount() > 0) throw new IllegalStateException();
    if (lock.getWriteHoldCount() > 0) throw new IllegalStateException();
    
    long start = now();
    if (readOnly) {
        lock.readLock().lock();
        logDuration(LOG, "Waiting for read lock", start);
    } else {
        lock.writeLock().lock();
        logDuration(LOG, "Waiting for write lock", start);
    }
    try {
        return new Transaction(db.startTransaction(), readOnly);
    } catch (DbException | RuntimeException e) {
        if (readOnly) lock.readLock().unlock();
        else lock.writeLock().unlock();
        throw e;
    }
}
```

### 数据库迁移机制
```java
// 应用数据库迁移
for (Migration<Connection> m : getMigrations()) {
    int start = m.getStartVersion(), end = m.getEndVersion();
    if (start == dataSchemaVersion) {
        if (LOG.isLoggable(INFO))
            LOG.info("Migrating from schema " + start + " to " + end);
        if (listener != null) listener.onDatabaseMigration();
        // 应用迁移
        m.migrate(txn);
        // 存储新的schema版本
        storeSchemaVersion(txn, end);
        dataSchemaVersion = end;
    }
}

// 迁移列表
List<Migration<Connection>> getMigrations() {
    return asList(
        new Migration38_39(),
        new Migration39_40(),
        new Migration40_41(dbTypes),
        new Migration41_42(dbTypes),
        new Migration42_43(dbTypes),
        new Migration43_44(dbTypes),
        new Migration44_45(),
        new Migration45_46(),
        new Migration46_47(dbTypes),
        new Migration47_48(),
        new Migration48_49(),
        new Migration49_50()
    );
}
```

## 🧹 自动清理机制

### CleanupManager - 清理管理器
```java
@Override
public void eventOccurred(Event e) {
    if (e instanceof CleanupTimerStartedEvent) {
        CleanupTimerStartedEvent a = (CleanupTimerStartedEvent) e;
        maybeScheduleTask(a.getCleanupDeadline());
    }
}

private void maybeScheduleTask(long deadline) {
    synchronized (lock) {
        for (CleanupTask task : pending) {
            if (task.deadline <= deadline) return;
        }
        CleanupTask task = new CleanupTask(deadline);
        pending.add(task);
        scheduleTask(task);
    }
}

private void scheduleTask(CleanupTask task) {
    long now = clock.currentTimeMillis();
    long delay = max(0, task.deadline - now + BATCH_DELAY_MS);
    if (LOG.isLoggable(INFO)) {
        LOG.info("Scheduling cleanup task in " + delay + " ms");
    }
    taskScheduler.schedule(() -> deleteMessagesAndScheduleNextTask(task),
            dbExecutor, delay, MILLISECONDS);
}
```

### 清理任务执行
```java
private void deleteMessagesAndScheduleNextTask(CleanupTask task) {
    try {
        synchronized (lock) {
            pending.remove(task);
        }
        long deadline = db.transactionWithResult(false, txn -> {
            deleteMessages(txn);
            return db.getNextCleanupDeadline(txn);
        });
        if (deadline != NO_CLEANUP_DEADLINE) {
            maybeScheduleTask(deadline);
        }
    } catch (DbException e) {
        logException(LOG, WARNING, e);
    }
}

private void deleteMessages(Transaction txn) throws DbException {
    Map<GroupId, Collection<MessageId>> ids = db.getMessagesToDelete(txn);
    for (Entry<GroupId, Collection<MessageId>> e : ids.entrySet()) {
        GroupId groupId = e.getKey();
        Collection<MessageId> messageIds = e.getValue();
        if (LOG.isLoggable(INFO)) {
            LOG.info(messageIds.size() + " messages to delete");
        }
        for (MessageId m : messageIds) db.stopCleanupTimer(txn, m);
        Group group = db.getGroup(txn, groupId);
        ClientMajorVersion cv = new ClientMajorVersion(group.getClientId(),
                group.getMajorVersion());
        CleanupHook hook = hooks.get(cv);
        if (hook == null) {
            throw new IllegalStateException("No cleanup hook for " + cv);
        }
        hook.deleteMessages(txn, groupId, messageIds);
    }
}
```

### 清理定时器操作
```java
@Override
public long startCleanupTimer(Connection txn, MessageId m) throws DbException {
    long now = clock.currentTimeMillis();
    PreparedStatement ps = null;
    ResultSet rs = null;
    try {
        String sql = "UPDATE messages"
                + " SET cleanupDeadline = ? + cleanupTimerDuration"
                + " WHERE messageId = ?"
                + " AND cleanupTimerDuration IS NOT NULL"
                + " AND cleanupDeadline IS NULL";
        ps = txn.prepareStatement(sql);
        ps.setLong(1, now);
        ps.setBytes(2, m.getBytes());
        int affected = ps.executeUpdate();
        if (affected < 0 || affected > 1) throw new DbStateException();
        ps.close();
        if (affected == 0) return TIMER_NOT_STARTED;
        
        sql = "SELECT cleanupDeadline FROM messages WHERE messageId = ?";
        ps = txn.prepareStatement(sql);
        ps.setBytes(1, m.getBytes());
        rs = ps.executeQuery();
        if (!rs.next()) throw new DbStateException();
        long deadline = rs.getLong(1);
        if (rs.next()) throw new DbStateException();
        rs.close();
        ps.close();
        return deadline;
    } catch (SQLException e) {
        tryToClose(rs, LOG, WARNING);
        tryToClose(ps, LOG, WARNING);
        throw new DbException(e);
    }
}
```

## 📡 事件系统

### EventBus - 事件总线实现
```java
@ThreadSafe
@NotNullByDefault
class EventBusImpl implements EventBus {
    private final Collection<EventListener> listeners =
            new CopyOnWriteArrayList<>();
    private final Executor eventExecutor;

    @Inject
    EventBusImpl(@EventExecutor Executor eventExecutor) {
        this.eventExecutor = eventExecutor;
    }

    @Override
    public void addListener(EventListener l) {
        listeners.add(l);
    }

    @Override
    public void removeListener(EventListener l) {
        listeners.remove(l);
    }

    @Override
    public void broadcast(Event e) {
        eventExecutor.execute(() -> {
            for (EventListener l : listeners) l.eventOccurred(e);
        });
    }
}
```

### 事件监听器示例
```java
@Override
public void eventOccurred(Event e) {
    if (e instanceof SettingsUpdatedEvent) {
        SettingsUpdatedEvent s = (SettingsUpdatedEvent) e;
        if (s.getNamespace().equals(ID.getString()))
            ioExecutor.execute(() -> onSettingsUpdated(s.getSettings()));
    } else if (e instanceof KeyAgreementListeningEvent) {
        connectionLimiter.startLimiting();
    } else if (e instanceof KeyAgreementStoppedListeningEvent) {
        connectionLimiter.endLimiting();
    } else if (e instanceof RemoteTransportPropertiesUpdatedEvent) {
        RemoteTransportPropertiesUpdatedEvent r =
                (RemoteTransportPropertiesUpdatedEvent) e;
        if (r.getTransportId().equals(ID)) {
            ioExecutor.execute(this::updateProperties);
        }
    }
}
```

## 🚀 生命周期管理

### LifecycleManager - 服务启动
```java
@Override
public StartResult startServices(SecretKey dbKey) {
    if (!state.compareAndSet(CREATED, STARTING)) {
        LOG.warning("Already running");
        return ALREADY_RUNNING;
    }
    
    long now = clock.currentTimeMillis();
    if (now < MIN_REASONABLE_TIME_MS || now > MAX_REASONABLE_TIME_MS) {
        if (LOG.isLoggable(WARNING)) {
            LOG.warning("System clock is unreasonable: " + now);
        }
        return CLOCK_ERROR;
    }

    try {
        LOG.info("Opening database");
        state.set(STARTING);
        eventBus.broadcast(new LifecycleEvent(STARTING));
        
        boolean reopened = db.open(dbKey, this);
        
        LOG.info("Starting services");
        state.set(STARTING_SERVICES);
        dbLatch.countDown();
        eventBus.broadcast(new LifecycleEvent(STARTING_SERVICES));

        for (Service s : services) {
            start = now();
            s.startService();
            if (LOG.isLoggable(FINE)) {
                logDuration(LOG, "Starting service "
                        + s.getClass().getSimpleName(), start);
            }
        }

        state.set(RUNNING);
        startupLatch.countDown();
        eventBus.broadcast(new LifecycleEvent(RUNNING));
        return SUCCESS;
    } catch (DataTooOldException e) {
        logException(LOG, WARNING, e);
        return DATA_TOO_OLD_ERROR;
    } catch (DataTooNewException e) {
        logException(LOG, WARNING, e);
        return DATA_TOO_NEW_ERROR;
    } catch (DbException e) {
        logException(LOG, WARNING, e);
        return DB_ERROR;
    } catch (ServiceException e) {
        logException(LOG, WARNING, e);
        return SERVICE_ERROR;
    }
}
```

---

这些代码片段展示了Briar项目的核心实现细节，包括依赖注入、数据库操作、事件处理、自动清理和生命周期管理等关键功能的具体实现方法。
