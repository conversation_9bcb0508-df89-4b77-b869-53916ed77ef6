# Briar技术架构图

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "Briar应用层"
        BA[briar-android] --> BC[briar-core]
        BH[briar-headless] --> BC
        BC --> BAPI[briar-api]
    end
    
    subgraph "Bramble框架层"
        BC --> BMAPI[bramble-api]
        BMAPI --> BMC[bramble-core]
        BMC --> BMJ[bramble-java]
        BMC --> BMA[bramble-android]
    end
    
    subgraph "外部依赖"
        BMJ --> H2[H2 Database]
        BMJ --> TOR[Tor Network]
        BMA --> ANDROID[Android SDK]
        BMC --> DAGGER[Dagger 2]
    end
```

## 📦 模块依赖关系

```mermaid
graph LR
    subgraph "API层"
        BAPI[briar-api]
        BMAPI[bramble-api]
    end
    
    subgraph "核心实现"
        BC[briar-core] --> BAPI
        BMC[bramble-core] --> BMAPI
        BC --> BMC
    end
    
    subgraph "平台适配"
        BA[briar-android] --> BC
        BH[briar-headless] --> BC
        BMJ[bramble-java] --> BMC
        BMA[bramble-android] --> BMC
    end
```

## 🔧 Bramble核心模块架构

```mermaid
graph TB
    subgraph "Bramble Core Modules"
        CM[CleanupModule]
        DM[DatabaseModule]
        EM[EventModule]
        CRM[CryptoModule]
        TM[TransportModule]
        PM[PluginModule]
        SM[SyncModule]
        LM[LifecycleModule]
        
        LM --> DM
        LM --> EM
        TM --> PM
        SM --> CRM
        CM --> DM
    end
```

## 🗄️ 数据库架构图

```mermaid
erDiagram
    CONTACTS {
        int contactId PK
        hash authorId
        string name
        binary publicKey
        hash localAuthorId FK
        boolean verified
    }
    
    GROUPS {
        hash groupId PK
        string clientId
        int majorVersion
        binary descriptor
    }
    
    MESSAGES {
        hash messageId PK
        hash groupId FK
        bigint timestamp
        int state
        boolean shared
        boolean temporary
        bigint cleanupTimerDuration
        bigint cleanupDeadline
        blob raw
    }
    
    STATUSES {
        hash messageId PK,FK
        int contactId PK,FK
        hash groupId
        bigint timestamp
        boolean ack
        boolean seen
        boolean requested
        bigint expiry
        int txCount
    }
    
    CONTACTS ||--o{ STATUSES : "has status for"
    MESSAGES ||--o{ STATUSES : "has status"
    GROUPS ||--o{ MESSAGES : "contains"
```

## 🌐 网络通信架构

```mermaid
graph TB
    subgraph "应用层"
        APP[Application]
    end
    
    subgraph "连接管理层"
        CM[ConnectionManager]
        SM[SyncManager]
    end
    
    subgraph "传输插件层"
        TP[TorPlugin]
        TCP[TcpPlugin]
        BT[BluetoothPlugin]
        MB[MailboxPlugin]
    end
    
    subgraph "网络层"
        TOR[Tor Network]
        WIFI[WiFi/LAN]
        BLE[Bluetooth]
        MAIL[Email Relay]
    end
    
    APP --> CM
    CM --> SM
    SM --> TP
    SM --> TCP
    SM --> BT
    SM --> MB
    
    TP --> TOR
    TCP --> WIFI
    BT --> BLE
    MB --> MAIL
```

## 🔐 加密系统架构

```mermaid
graph TB
    subgraph "密钥层次"
        RK[Root Key]
        IK[Identity Key - Ed25519]
        AK[Agreement Key - Curve25519]
        
        subgraph "传输密钥"
            TK[Tag Key]
            HK[Header Key]
            FK[Frame Key]
        end
    end
    
    subgraph "加密流程"
        MSG[Message] --> ENC[Encrypt]
        ENC --> FRAME[Frame]
        FRAME --> MAC[Add MAC]
        MAC --> SEND[Send]
    end
    
    RK --> IK
    RK --> AK
    AK --> TK
    AK --> HK
    AK --> FK
    
    FK --> ENC
    HK --> ENC
    TK --> FRAME
```

## 📡 消息同步流程

```mermaid
sequenceDiagram
    participant A as Node A
    participant B as Node B
    
    Note over A,B: 连接建立
    A->>B: 版本协商
    B->>A: 版本确认
    
    Note over A,B: 优先级交换
    A->>B: 优先级随机数
    B->>A: 优先级随机数
    
    Note over A,B: 状态同步
    A->>B: Offer (可发送消息列表)
    B->>A: Request (请求消息列表)
    
    Note over A,B: 数据传输
    A->>B: Message Batch
    B->>A: Ack (确认接收)
    
    Note over A,B: 清理
    A->>B: 连接关闭
```

## 🔄 事件系统架构

```mermaid
graph LR
    subgraph "事件生产者"
        DB[Database]
        NET[Network]
        LIFE[Lifecycle]
        PLUGIN[Plugins]
    end
    
    subgraph "事件总线"
        EB[EventBus]
        EE[EventExecutor]
    end
    
    subgraph "事件消费者"
        CM[CleanupManager]
        SM[SyncManager]
        UI[UI Components]
        PLUGIN2[Other Plugins]
    end
    
    DB --> EB
    NET --> EB
    LIFE --> EB
    PLUGIN --> EB
    
    EB --> EE
    EE --> CM
    EE --> SM
    EE --> UI
    EE --> PLUGIN2
```

## 🚀 生命周期管理

```mermaid
stateDiagram-v2
    [*] --> CREATED
    CREATED --> STARTING : startServices()
    STARTING --> MIGRATING_DATABASE : 需要迁移
    STARTING --> STARTING_SERVICES : 无需迁移
    MIGRATING_DATABASE --> STARTING_SERVICES : 迁移完成
    STARTING_SERVICES --> RUNNING : 服务启动完成
    RUNNING --> STOPPING : stopServices()
    STOPPING --> STOPPED : 停止完成
    STOPPED --> [*]
```

## 🧹 自动清理机制

```mermaid
graph TB
    subgraph "清理触发"
        MSG[Message Created]
        TIMER[Set Timer Duration]
        ACK[Message Acknowledged]
        START[Start Timer]
    end
    
    subgraph "清理调度"
        EVENT[CleanupTimerStartedEvent]
        MANAGER[CleanupManager]
        SCHEDULE[Schedule Task]
    end
    
    subgraph "清理执行"
        DEADLINE[Check Deadline]
        HOOK[Call CleanupHook]
        DELETE[Delete Message]
    end
    
    MSG --> TIMER
    TIMER --> ACK
    ACK --> START
    START --> EVENT
    EVENT --> MANAGER
    MANAGER --> SCHEDULE
    SCHEDULE --> DEADLINE
    DEADLINE --> HOOK
    HOOK --> DELETE
```

## 🔍 数据库索引策略

```mermaid
graph TB
    subgraph "主要索引"
        IDX1[contactsByAuthorId]
        IDX2[statusesByContactIdTimestamp]
        IDX3[messagesByCleanupDeadline]
        IDX4[statusesByContactIdGroupId]
    end
    
    subgraph "查询优化"
        Q1[联系人查找] --> IDX1
        Q2[消息时间排序] --> IDX2
        Q3[清理任务查询] --> IDX3
        Q4[群组消息状态] --> IDX4
    end
```

---

这些架构图展示了Briar项目的技术架构全貌，从整体模块关系到具体的数据流和处理流程，帮助我们更好地理解系统的设计思路和实现细节。
